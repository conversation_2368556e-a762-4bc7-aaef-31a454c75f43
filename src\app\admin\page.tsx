'use client';

import { <PERSON>a<PERSON><PERSON><PERSON>, <PERSON>aUser<PERSON>, <PERSON><PERSON><PERSON><PERSON>, FaThum<PERSON><PERSON>p, FaList } from 'react-icons/fa';

// Mock data for demonstration
const stats = {
  totalVideos: 1250,
  totalCategories: 24,
  totalUsers: 5680,
  totalViews: 12500000,
  totalLikes: 3200000,
  recentVideos: [
    { id: '1', title: 'Hot blonde in action with her boyfriend', views: 12500, date: '2023-05-10' },
    { id: '2', title: '<PERSON><PERSON><PERSON> gets wild in the bedroom', views: 9800, date: '2023-05-09' },
    { id: '3', title: 'Steamy shower scene with two girls', views: 15000, date: '2023-05-08' },
    { id: '4', title: 'Office romance turns into something more', views: 7500, date: '2023-05-07' },
    { id: '5', title: 'Passionate couple on vacation', views: 21000, date: '2023-05-06' },
  ],
  topCategories: [
    { name: 'Amateur', videos: 320, views: 4500000 },
    { name: '<PERSON><PERSON>', videos: 280, views: 3800000 },
    { name: '<PERSON><PERSON>', videos: 250, views: 3500000 },
    { name: 'Threesome', videos: 180, views: 2800000 },
    { name: '<PERSON><PERSON><PERSON>', videos: 210, views: 2500000 },
  ]
};

// Format numbers with commas
const formatNumber = (num: number) => {
  return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
};

export default function AdminDashboard() {
  return (
    <div>
      <h1 className="text-2xl font-bold text-white mb-6">Dashboard Overview</h1>
      
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-6 mb-8">
        <div className="bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-500 bg-opacity-20 text-blue-500 mr-4">
              <FaVideo className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Videos</p>
              <h3 className="text-2xl font-bold text-white">{formatNumber(stats.totalVideos)}</h3>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-500 bg-opacity-20 text-green-500 mr-4">
              <FaList className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Categories</p>
              <h3 className="text-2xl font-bold text-white">{formatNumber(stats.totalCategories)}</h3>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-purple-500 bg-opacity-20 text-purple-500 mr-4">
              <FaUsers className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Users</p>
              <h3 className="text-2xl font-bold text-white">{formatNumber(stats.totalUsers)}</h3>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-yellow-500 bg-opacity-20 text-yellow-500 mr-4">
              <FaEye className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Views</p>
              <h3 className="text-2xl font-bold text-white">{formatNumber(stats.totalViews)}</h3>
            </div>
          </div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-6 shadow-md">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-red-500 bg-opacity-20 text-red-500 mr-4">
              <FaThumbsUp className="w-6 h-6" />
            </div>
            <div>
              <p className="text-sm text-gray-400">Total Likes</p>
              <h3 className="text-2xl font-bold text-white">{formatNumber(stats.totalLikes)}</h3>
            </div>
          </div>
        </div>
      </div>
      
      {/* Recent Videos and Top Categories */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Videos */}
        <div className="bg-gray-800 rounded-lg shadow-md">
          <div className="px-6 py-4 border-b border-gray-700">
            <h2 className="text-lg font-semibold text-white">Recent Videos</h2>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-gray-400 text-sm">
                    <th className="pb-3">Title</th>
                    <th className="pb-3">Views</th>
                    <th className="pb-3">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.recentVideos.map((video) => (
                    <tr key={video.id} className="border-t border-gray-700">
                      <td className="py-3 text-white">{video.title}</td>
                      <td className="py-3 text-gray-400">{formatNumber(video.views)}</td>
                      <td className="py-3 text-gray-400">{video.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
        
        {/* Top Categories */}
        <div className="bg-gray-800 rounded-lg shadow-md">
          <div className="px-6 py-4 border-b border-gray-700">
            <h2 className="text-lg font-semibold text-white">Top Categories</h2>
          </div>
          <div className="p-6">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="text-left text-gray-400 text-sm">
                    <th className="pb-3">Category</th>
                    <th className="pb-3">Videos</th>
                    <th className="pb-3">Views</th>
                  </tr>
                </thead>
                <tbody>
                  {stats.topCategories.map((category) => (
                    <tr key={category.name} className="border-t border-gray-700">
                      <td className="py-3 text-white">{category.name}</td>
                      <td className="py-3 text-gray-400">{formatNumber(category.videos)}</td>
                      <td className="py-3 text-gray-400">{formatNumber(category.views)}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
