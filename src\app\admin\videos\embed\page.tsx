'use client';

import { useState, useEffect } from 'react';
import { FaSave, FaTimes, FaLink, FaCode } from 'react-icons/fa';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import axios from 'axios';

// Interface for category
interface Category {
  _id: string;
  name: string;
  slug: string;
}

// Interface for source site
interface SourceSite {
  id: string;
  name: string;
  baseUrl: string;
}

export default function EmbedVideo() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [previewLoading, setPreviewLoading] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  // Available source sites
  const sourceSites: SourceSite[] = [
    { id: 'xhamster', name: 'xHamster', baseUrl: 'https://xhamster.com' },
    { id: 'pornhub', name: '<PERSON>rnH<PERSON>', baseUrl: 'https://pornhub.com' },
    { id: 'xvideos', name: 'XVideos', baseUrl: 'https://xvideos.com' },
    { id: 'redtube', name: 'RedTube', baseUrl: 'https://redtube.com' },
    { id: 'youporn', name: 'YouPorn', baseUrl: 'https://youporn.com' },
  ];

  const [formData, setFormData] = useState({
    title: '',
    description: '',
    sourceUrl: '',
    categories: [] as string[],
    tags: '',
    sourceSite: sourceSites[0].id,
    isPublished: true,
    isVerified: true
  });

  // Auto-extracted data from embed URL
  const [extractedData, setExtractedData] = useState({
    thumbnailUrl: '',
    duration: 0,
    title: ''
  });

  // Generated embed code for preview
  const [generatedEmbedCode, setGeneratedEmbedCode] = useState('');

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('Fetching categories...');
        const response = await axios.get('/api/categories');

        if (response.data && response.data.categories) {
          console.log('Categories fetched successfully:', response.data.categories.length);
          setCategories(response.data.categories);
        } else {
          console.error('No categories found in response:', response.data);
          // Set empty array to avoid undefined errors
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        // Set empty array to avoid undefined errors
        setCategories([]);
      }
    };

    fetchCategories();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleCategoryChange = (categoryId: string) => {
    setFormData(prev => {
      const categories = [...prev.categories];
      if (categories.includes(categoryId)) {
        return {
          ...prev,
          categories: categories.filter(c => c !== categoryId)
        };
      } else {
        return {
          ...prev,
          categories: [...categories, categoryId]
        };
      }
    });
  };

  const handlePreview = () => {
    if (!formData.sourceUrl) {
      setErrors(prev => ({
        ...prev,
        sourceUrl: 'Source URL is required for preview'
      }));
      return;
    }

    if (!isValidUrl(formData.sourceUrl)) {
      setErrors(prev => ({
        ...prev,
        sourceUrl: 'Please enter a valid URL'
      }));
      return;
    }

    setPreviewLoading(true);

    // Generate embed code from source URL
    const embedCode = generateEmbedCode(formData.sourceUrl, formData.sourceSite);
    setGeneratedEmbedCode(embedCode);

    // Simulate loading for preview
    setTimeout(() => {
      setShowPreview(true);
      setPreviewLoading(false);
    }, 500);
  };

  // Auto-generate embed code and extract metadata when source URL or site changes
  useEffect(() => {
    const processUrl = async () => {
      if (formData.sourceUrl && isValidUrl(formData.sourceUrl)) {
        const embedCode = generateEmbedCode(formData.sourceUrl, formData.sourceSite);
        setGeneratedEmbedCode(embedCode);

        // Extract metadata from the URL
        const metadata = await extractVideoMetadata(formData.sourceUrl, formData.sourceSite);
        if (metadata) {
          setExtractedData(metadata);
          // Auto-fill title if it's empty
          if (!formData.title) {
            setFormData(prev => ({ ...prev, title: metadata.title }));
          }
        }
      } else {
        setGeneratedEmbedCode('');
        setExtractedData({ thumbnailUrl: '', duration: 0, title: '' });
      }
    };

    processUrl();
  }, [formData.sourceUrl, formData.sourceSite]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.sourceUrl.trim()) {
      newErrors.sourceUrl = 'Source URL is required';
    } else if (!isValidUrl(formData.sourceUrl)) {
      newErrors.sourceUrl = 'Please enter a valid URL';
    }

    // Check if we can generate an embed code
    if (formData.sourceUrl.trim() && isValidUrl(formData.sourceUrl)) {
      const embedCode = generateEmbedCode(formData.sourceUrl, formData.sourceSite);
      if (!embedCode) {
        newErrors.sourceUrl = 'Could not generate embed code from this URL. Please check the URL format.';
      }
    }

    // Thumbnail and duration are now auto-extracted, so no validation needed

    if (formData.categories.length === 0) {
      newErrors.categories = 'At least one category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const isValidUrl = (url: string) => {
    try {
      new URL(url);
      return true;
    } catch (e) {
      return false;
    }
  };

  // Function to extract metadata from video URL
  const extractVideoMetadata = async (url: string, sourceSite: string) => {
    if (!url || !isValidUrl(url)) return null;

    try {
      // For demo purposes, we'll generate mock metadata
      // In a real application, you would use APIs or web scraping
      const videoId = extractVideoId(url, sourceSite);
      if (!videoId) return null;

      // Generate mock thumbnail and duration based on video ID
      const mockThumbnail = `https://picsum.photos/640/360?random=${videoId}`;
      const mockDuration = Math.floor(Math.random() * 1800) + 300; // 5-35 minutes
      const mockTitle = `Video ${videoId}`;

      return {
        thumbnailUrl: mockThumbnail,
        duration: mockDuration,
        title: mockTitle
      };
    } catch (error) {
      console.error('Error extracting video metadata:', error);
      return null;
    }
  };

  // Helper function to extract video ID from URL
  const extractVideoId = (url: string, sourceSite: string): string => {
    try {
      const urlObj = new URL(url);

      switch(sourceSite) {
        case 'xhamster':
          return url.split('/').pop()?.split('-').pop() || '';
        case 'pornhub':
          return urlObj.searchParams.get('viewkey') || '';
        case 'xvideos':
          return url.split('/')[3]?.replace('video', '') || '';
        case 'redtube':
          return url.split('/').pop() || '';
        case 'youporn':
          return url.split('/watch/')[1]?.split('/')[0] || '';
        default:
          return '';
      }
    } catch (error) {
      return '';
    }
  };

  // Function to generate embed code from source URL (client-side version)
  const generateEmbedCode = (url: string, sourceSite: string): string => {
    if (!url || !isValidUrl(url)) return '';

    // Extract video ID from URL based on the source site
    let videoId = '';
    let embedCode = '';

    try {
      const urlObj = new URL(url);

      switch(sourceSite) {
        case 'xhamster':
          // Example: https://xhamster.com/videos/video-title-12345678
          videoId = url.split('/').pop()?.split('-').pop() || '';
          if (videoId) {
            embedCode = `<iframe src="https://xhamster.com/embed/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
          }
          break;

        case 'pornhub':
          // Example: https://www.pornhub.com/view_video.php?viewkey=ph123456789
          if (urlObj.searchParams.has('viewkey')) {
            videoId = urlObj.searchParams.get('viewkey') || '';
            embedCode = `<iframe src="https://www.pornhub.com/embed/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
          }
          break;

        case 'xvideos':
          // Example: https://www.xvideos.com/video12345678/video_title
          videoId = url.split('/')[3]?.replace('video', '') || '';
          if (videoId) {
            embedCode = `<iframe src="https://www.xvideos.com/embedframe/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
          }
          break;

        case 'redtube':
          // Example: https://www.redtube.com/12345678
          videoId = url.split('/').pop() || '';
          if (videoId && !isNaN(Number(videoId))) {
            embedCode = `<iframe src="https://embed.redtube.com/?id=${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
          }
          break;

        case 'youporn':
          // Example: https://www.youporn.com/watch/12345678/video-title/
          videoId = url.split('/watch/')[1]?.split('/')[0] || '';
          if (videoId) {
            embedCode = `<iframe src="https://www.youporn.com/embed/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
          }
          break;

        default:
          // Generic fallback - create a link to the original content
          embedCode = `<div class="external-video"><p>This video is hosted on ${sourceSite}.</p><a href="${url}" target="_blank" rel="noopener noreferrer">Watch on ${sourceSite}</a></div>`;
      }

      return embedCode;
    } catch (error) {
      console.error('Error generating embed code:', error);
      return `<div class="external-video"><a href="${url}" target="_blank" rel="noopener noreferrer">Watch on ${sourceSite}</a></div>`;
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        setLoading(true);

        // Prepare tags array from comma-separated string
        const tags = formData.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);

        // Create a slug from the title
        const slug = formData.title
          .toLowerCase()
          .replace(/[^\w\s]/gi, '')
          .replace(/\s+/g, '-')
          .concat('-', Date.now().toString().slice(-4));

        // Generate the embed code from the source URL
        const embedCode = generateEmbedCode(formData.sourceUrl, formData.sourceSite);

        if (!embedCode) {
          setErrors(prev => ({
            ...prev,
            sourceUrl: 'Could not generate embed code from this URL. Please check the URL format.'
          }));
          setLoading(false);
          return;
        }

        // Prepare the data to send to the API
        const videoData = {
          title: formData.title,
          description: formData.description,
          slug,
          duration: extractedData.duration || 600, // Use extracted duration or default
          thumbnailUrl: extractedData.thumbnailUrl,
          sourceUrl: formData.sourceUrl,
          embedUrl: embedCode, // Use the generated embed code
          sourceSite: formData.sourceSite,
          categories: formData.categories,
          tags,
          isPublished: formData.isPublished,
          isVerified: formData.isVerified,
          // Let the server use the session user ID
          views: 0,
          likes: 0,
          dislikes: 0
        };

        // Send the data to the API
        const response = await axios.post('/api/videos/embed', videoData);

        // Redirect to the videos list page
        router.push('/admin/videos');

      } catch (error: any) {
        console.error('Error adding embedded video:', error);

        // Log more detailed error information
        if (error.response) {
          // The request was made and the server responded with a status code
          // that falls out of the range of 2xx
          console.error('Error response data:', error.response.data);
          console.error('Error response status:', error.response.status);
          console.error('Error response headers:', error.response.headers);

          // Show more specific error message if available
          if (error.response.data && error.response.data.error) {
            alert(`Failed to add embedded video: ${error.response.data.error}`);
          } else {
            alert(`Failed to add embedded video. Server returned status ${error.response.status}`);
          }
        } else if (error.request) {
          // The request was made but no response was received
          console.error('Error request:', error.request);
          alert('Failed to add embedded video. No response received from server.');
        } else {
          // Something happened in setting up the request that triggered an Error
          console.error('Error message:', error.message);
          alert(`Failed to add embedded video: ${error.message}`);
        }
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Embed External Video</h1>
        <Link href="/admin/videos" className="btn-secondary flex items-center">
          <FaTimes className="mr-2" /> Cancel
        </Link>
      </div>

      <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div>
            <div className="mb-4">
              <label className="block text-white mb-2">Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={`input-field w-full ${errors.title ? 'border border-red-500' : ''}`}
                placeholder="Enter video title"
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="input-field w-full h-32"
                placeholder="Enter video description"
              ></textarea>
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Source Site *</label>
              <select
                name="sourceSite"
                value={formData.sourceSite}
                onChange={handleChange}
                className="input-field w-full"
              >
                {sourceSites.map(site => (
                  <option key={site.id} value={site.id}>{site.name}</option>
                ))}
              </select>
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Source URL *</label>
              <div className="flex items-center">
                <div className="relative w-full">
                  <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
                    <FaLink />
                  </div>
                  <input
                    type="text"
                    name="sourceUrl"
                    value={formData.sourceUrl}
                    onChange={handleChange}
                    className={`input-field w-full pl-10 ${errors.sourceUrl ? 'border border-red-500' : ''}`}
                    placeholder="https://xhamster.com/videos/example-12345"
                  />
                </div>
              </div>
              {errors.sourceUrl && <p className="text-red-500 text-sm mt-1">{errors.sourceUrl}</p>}
            </div>

            <div className="mb-4">
              <div className="flex justify-between items-center mb-2">
                <label className="block text-white">Preview</label>
                <button
                  type="button"
                  onClick={handlePreview}
                  className="btn-secondary text-sm"
                  disabled={previewLoading || !formData.sourceUrl}
                >
                  {previewLoading ? 'Loading...' : 'Refresh Preview'}
                </button>
              </div>

              {!formData.sourceUrl ? (
                <div className="bg-gray-700 rounded-lg p-4 text-center">
                  <p className="text-gray-400">Enter a source URL to see preview</p>
                </div>
              ) : !isValidUrl(formData.sourceUrl) ? (
                <div className="bg-gray-700 rounded-lg p-4 text-center">
                  <p className="text-yellow-400">Please enter a valid URL</p>
                </div>
              ) : !generatedEmbedCode ? (
                <div className="bg-gray-700 rounded-lg p-4 text-center">
                  <p className="text-yellow-400">Could not generate embed code from this URL</p>
                  <p className="text-gray-400 text-sm mt-2">Please check that the URL format is correct for the selected source site</p>
                </div>
              ) : (
                <div className="bg-black rounded-lg p-2">
                  <div className="aspect-video w-full" dangerouslySetInnerHTML={{ __html: generatedEmbedCode }}></div>
                </div>
              )}

              <div className="mt-2 text-xs text-gray-400">
                <p>Embed code will be automatically generated from the source URL</p>
              </div>
            </div>
          </div>

          {/* Right Column */}
          <div>

            <div className="mb-4">
              <label className="block text-white mb-2">Categories *</label>
              <div className="bg-gray-700 p-4 rounded max-h-64 overflow-y-auto">
                {categories === undefined ? (
                  <div className="flex flex-col items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-500 mb-2"></div>
                    <p className="text-gray-400">Loading categories...</p>
                  </div>
                ) : categories.length > 0 ? (
                  <div className="grid grid-cols-2 gap-2">
                    {categories.map((category) => (
                      <div key={category._id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`category-${category._id}`}
                          checked={formData.categories.includes(category._id)}
                          onChange={() => handleCategoryChange(category._id)}
                          className="mr-2"
                        />
                        <label htmlFor={`category-${category._id}`} className="text-gray-300 cursor-pointer">
                          {category.name}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-yellow-400 mb-2">No categories found</p>
                    <p className="text-gray-400 text-sm">
                      Please make sure MongoDB is running and categories are created.
                      <br />
                      You can run the create-categories.js script to add initial categories.
                    </p>
                  </div>
                )}
              </div>
              {errors.categories && <p className="text-red-500 text-sm mt-1">{errors.categories}</p>}
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Tags (comma separated)</label>
              <input
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleChange}
                className="input-field w-full"
                placeholder="e.g., blonde, amateur, couple"
              />
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPublished"
                  name="isPublished"
                  checked={formData.isPublished}
                  onChange={handleCheckboxChange}
                  className="mr-2"
                />
                <label htmlFor="isPublished" className="text-white cursor-pointer">
                  Published (video will be visible on the site)
                </label>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isVerified"
                  name="isVerified"
                  checked={formData.isVerified}
                  onChange={handleCheckboxChange}
                  className="mr-2"
                />
                <label htmlFor="isVerified" className="text-white cursor-pointer">
                  Age Verified (confirm all performers are 18+)
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            type="submit"
            className={`btn-primary flex items-center ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading}
          >
            <FaSave className="mr-2" /> {loading ? 'Saving...' : 'Save Embedded Video'}
          </button>
        </div>
      </form>
    </div>
  );
}
