import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Category from '@/app/models/Category';
import Video from '@/app/models/Video';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Connect to the database
    await dbConnect();

    const { searchParams } = new URL(req.url);
    const includeInactive = searchParams.get('includeInactive') === 'true';
    const withCounts = searchParams.get('withCounts') === 'true';

    // Build query
    const query = includeInactive ? {} : { isActive: true };

    // Get categories
    const categories = await Category.find(query)
      .sort({ name: 1 });

    // If withCounts is true, get video counts for each category
    if (withCounts) {
      const categoriesWithCounts = await Promise.all(
        categories.map(async (category) => {
          const videoCount = await Video.countDocuments({
            categories: category._id,
            isPublished: true
          });

          return {
            ...category.toObject(),
            videoCount
          };
        })
      );

      return NextResponse.json({ categories: categoriesWithCounts });
    }

    return NextResponse.json({ categories });
  } catch (error: any) {
    console.error('Error fetching categories:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch categories' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication and authorization
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Only admins can create categories
    if (!(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Forbidden - Admin access required' },
        { status: 403 }
      );
    }

    const body = await req.json();

    // Connect to the database
    await dbConnect();

    // Create new category
    const category = await Category.create(body);

    return NextResponse.json(category, { status: 201 });
  } catch (error: any) {
    console.error('Error creating category:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to create category' },
      { status: 500 }
    );
  }
}
