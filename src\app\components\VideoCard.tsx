'use client';

import Link from 'next/link';
import { FaEye, FaThumbsUp } from 'react-icons/fa';
import VideoThumbnail from './VideoThumbnail';
import { formatDuration, formatCount } from '@/app/utils/formatters';

interface VideoCardProps {
  id: string;
  title: string;
  thumbnail: string;
  duration: number;
  views: number;
  likes: number;
  slug: string;
  isCompact?: boolean;
  videoUrl?: string;
}

const VideoCard: React.FC<VideoCardProps> = ({
  id,
  title,
  thumbnail,
  duration,
  views,
  likes,
  slug,
  isCompact = false,
  videoUrl,
}) => {
  // We now use the imported formatters

  if (isCompact) {
    return (
      <div className="group">
        <Link href={`/videos/${slug}`} className="block">
          <div className="relative overflow-hidden rounded-xl bg-gradient-to-r from-gray-700 to-gray-800 border border-gray-600 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-purple-500/20">
            <img
              src={thumbnail}
              alt={title}
              className="w-full aspect-video object-cover group-hover:scale-110 transition-transform duration-500"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded font-semibold">
              {formatDuration(duration)}
            </div>
          </div>
          <div className="mt-3">
            <h3 className="text-white text-sm font-medium line-clamp-2 group-hover:text-purple-400 transition-colors duration-300">
              {title}
            </h3>
            <div className="flex items-center text-gray-400 text-xs mt-2 space-x-3">
              <span className="flex items-center">
                <FaEye className="mr-1 text-purple-400" />
                <span className="font-semibold">{formatCount(views)}</span>
              </span>
            </div>
          </div>
        </Link>
      </div>
    );
  }

  return (
    <div className="group">
      <Link href={`/videos/${slug}`} className="block">
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl overflow-hidden border border-gray-700 hover:border-purple-500/50 transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-purple-500/20">
          <div className="relative overflow-hidden">
            <VideoThumbnail
              title={title}
              duration={duration}
              videoUrl={videoUrl}
            />
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="absolute bottom-3 right-3 bg-black/70 text-white text-sm px-3 py-1 rounded font-semibold">
              {formatDuration(duration)}
            </div>
          </div>

          <div className="p-4">
            <h3 className="text-white font-medium line-clamp-2 group-hover:text-purple-400 transition-colors duration-300 mb-3">
              {title}
            </h3>

            <div className="flex items-center justify-between text-gray-400 text-sm">
              <span className="flex items-center">
                <div className="bg-purple-500/20 p-1 rounded-full mr-2">
                  <FaEye className="text-purple-400 text-xs" />
                </div>
                <span className="font-semibold">{formatCount(views)}</span>
              </span>
              <span className="flex items-center">
                <div className="bg-green-500/20 p-1 rounded-full mr-2">
                  <FaThumbsUp className="text-green-400 text-xs" />
                </div>
                <span className="font-semibold">{formatCount(likes)}</span>
              </span>
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default VideoCard;
