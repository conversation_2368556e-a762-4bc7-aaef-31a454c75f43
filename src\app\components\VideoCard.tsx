'use client';

import Link from 'next/link';
import { Fa<PERSON>ye, FaThumbsUp, FaCalendarAlt, FaTrophy } from 'react-icons/fa';
import VideoThumbnail from './VideoThumbnail';
import { formatDuration, formatCount } from '@/app/utils/formatters';

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl?: string;
  embedUrl?: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  tags: string[];
  uploadedBy: string;
  isPublished: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface VideoCardProps {
  video: Video;
  isCompact?: boolean;
  showRank?: boolean;
  rank?: number;
  showUploadDate?: boolean;
  // Legacy props for backward compatibility
  id?: string;
  title?: string;
  thumbnail?: string;
  duration?: number;
  views?: number;
  likes?: number;
  slug?: string;
  videoUrl?: string;
}

const VideoCard: React.FC<VideoCardProps> = ({
  video,
  isCompact = false,
  showRank = false,
  rank,
  showUploadDate = false,
  // Legacy props for backward compatibility
  id,
  title,
  thumbnail,
  duration,
  views,
  likes,
  slug,
  videoUrl,
}) => {
  // Support both new video object and legacy props
  const videoData = video || {
    _id: id || '',
    title: title || '',
    thumbnailUrl: thumbnail || '',
    duration: duration || 0,
    views: views || 0,
    likes: likes || 0,
    slug: slug || '',
    createdAt: new Date().toISOString(),
    categories: [],
    tags: []
  };

  if (isCompact) {
    return (
      <div className="group">
        <Link href={`/videos/${videoData.slug}`} className="block">
          <div className="relative overflow-hidden rounded-lg bg-ph-gray-800 hover:bg-ph-gray-700 transition-all duration-300 card-ph-hover">
            <img
              src={videoData.thumbnailUrl}
              alt={videoData.title}
              className="w-full aspect-video object-cover"
            />
            <div className="absolute bottom-2 right-2 bg-black/80 text-white text-xs px-2 py-1 rounded font-medium">
              {formatDuration(videoData.duration)}
            </div>
            {showRank && rank && (
              <div className="absolute top-2 left-2 bg-ph-orange-500 text-white text-xs px-2 py-1 rounded-full font-bold flex items-center">
                <FaTrophy className="mr-1" />
                #{rank}
              </div>
            )}
          </div>
          <div className="mt-3">
            <h3 className="text-white text-sm font-medium line-clamp-2 group-hover:text-ph-orange-500 transition-colors duration-300">
              {videoData.title}
            </h3>
            <div className="flex items-center justify-between mt-2 text-xs text-ph-gray-400">
              <div className="flex items-center space-x-3">
                <span className="flex items-center">
                  <FaEye className="mr-1" />
                  {formatCount(videoData.views)}
                </span>
                <span className="flex items-center">
                  <FaThumbsUp className="mr-1" />
                  {videoData.likes}%
                </span>
              </div>
              {showUploadDate && (
                <span className="flex items-center text-ph-gray-500">
                  <FaCalendarAlt className="mr-1" />
                  {new Date(videoData.createdAt).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
        </Link>
      </div>
    );
  }

  return (
    <div className="group">
      <Link href={`/videos/${videoData.slug}`} className="block">
        <div className="card-ph card-ph-hover">
          <div className="relative overflow-hidden">
            {showRank && rank && (
              <div className="absolute top-3 left-3 bg-ph-orange-500 text-white text-sm px-3 py-1 rounded-full font-bold flex items-center z-10">
                <FaTrophy className="mr-1" />
                #{rank}
              </div>
            )}
            <VideoThumbnail
              title={videoData.title}
              duration={videoData.duration}
              thumbnail={videoData.thumbnailUrl}
              videoUrl={videoData.videoUrl}
            />
          </div>

          <div className="p-4">
            <h3 className="text-white font-medium line-clamp-2 group-hover:text-ph-orange-500 transition-colors duration-300 mb-3">
              {videoData.title}
            </h3>

            <div className="flex items-center justify-between text-ph-gray-400 text-sm">
              <div className="flex items-center space-x-4">
                <span className="flex items-center">
                  <FaEye className="mr-1" />
                  <span className="font-semibold">{formatCount(videoData.views)}</span>
                </span>
                <span className="flex items-center text-green-400">
                  <FaThumbsUp className="mr-1" />
                  <span className="font-semibold">{videoData.likes}%</span>
                </span>
              </div>
              {showUploadDate && (
                <span className="flex items-center text-ph-gray-500 text-xs">
                  <FaCalendarAlt className="mr-1" />
                  {new Date(videoData.createdAt).toLocaleDateString()}
                </span>
              )}
            </div>
          </div>
        </div>
      </Link>
    </div>
  );
};

export default VideoCard;
