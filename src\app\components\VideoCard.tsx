'use client';

import Link from 'next/link';
import { FaEye, FaThumbsUp } from 'react-icons/fa';
import VideoThumbnail from './VideoThumbnail';
import { formatDuration, formatCount } from '@/app/utils/formatters';

interface VideoCardProps {
  id: string;
  title: string;
  thumbnail: string;
  duration: number;
  views: number;
  likes: number;
  slug: string;
}

const VideoCard: React.FC<VideoCardProps> = ({
  id,
  title,
  thumbnail,
  duration,
  views,
  likes,
  slug,
}) => {
  // We now use the imported formatters

  return (
    <div className="card group">
      <Link href={`/videos/${slug}`}>
        <VideoThumbnail
          title={title}
          duration={duration}
        />
      </Link>

      <div className="p-3">
        <Link href={`/videos/${slug}`}>
          <h3 className="text-white font-medium line-clamp-2 hover:text-red-500 transition-colors">
            {title}
          </h3>
        </Link>

        <div className="flex items-center text-gray-400 text-sm mt-2 space-x-3">
          <span className="flex items-center">
            <FaEye className="mr-1" />
            {formatCount(views)}
          </span>
          <span className="flex items-center">
            <FaThumbsUp className="mr-1" />
            {formatCount(likes)}
          </span>
        </div>
      </div>
    </div>
  );
};

export default VideoCard;
