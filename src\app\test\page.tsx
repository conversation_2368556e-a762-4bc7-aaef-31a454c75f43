'use client';

import { useState, useEffect } from 'react';

export default function TestPage() {
  const [serverTime, setServerTime] = useState<string>('Loading...');

  useEffect(() => {
    setServerTime(new Date().toLocaleTimeString());
    
    const interval = setInterval(() => {
      setServerTime(new Date().toLocaleTimeString());
    }, 1000);
    
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col items-center justify-center min-h-[50vh]">
      <h1 className="text-3xl font-bold mb-4">Server Test Page</h1>
      <p className="text-xl mb-8">If you can see this page, the Next.js server is running correctly.</p>
      
      <div className="bg-gray-800 p-6 rounded-lg shadow-lg">
        <h2 className="text-2xl font-semibold mb-2">Current Time</h2>
        <p className="text-4xl font-mono">{serverTime}</p>
      </div>
      
      <div className="mt-8 p-4 bg-yellow-900/30 text-yellow-200 rounded-lg max-w-2xl">
        <h3 className="font-bold mb-2">Troubleshooting Notes:</h3>
        <ul className="list-disc pl-5 space-y-2">
          <li>This page doesn't require database access, so it should load even if MongoDB is not working.</li>
          <li>If you can see this page but not other pages, the issue is likely with the database connection.</li>
          <li>Check that MongoDB is running and accessible at localhost:27017.</li>
          <li>Verify your .env.local file has the correct MongoDB connection string.</li>
        </ul>
      </div>
    </div>
  );
}
