import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import mongoose from 'mongoose';

// Get the Engagement model
let Engagement: mongoose.Model<any>;
try {
  Engagement = mongoose.model('Engagement');
} catch (e) {
  // If the model doesn't exist yet, return empty data
  // This will be properly initialized when the first engagement is tracked
  Engagement = mongoose.model('Engagement', new mongoose.Schema({}));
}

export async function GET(req: NextRequest) {
  try {
    // Check authentication and admin status
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Connect to the database
    await dbConnect();
    
    // Get time range from query params
    const { searchParams } = new URL(req.url);
    const timeRange = searchParams.get('timeRange') || '7d';
    
    // Calculate date range
    let startDate = new Date();
    switch (timeRange) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }
    
    // Get engagement metrics
    const totalEngagements = await Engagement.countDocuments({
      timestamp: { $gte: startDate }
    });
    
    const playCount = await Engagement.countDocuments({
      action: 'play',
      timestamp: { $gte: startDate }
    });
    
    const pauseCount = await Engagement.countDocuments({
      action: 'pause',
      timestamp: { $gte: startDate }
    });
    
    const seekCount = await Engagement.countDocuments({
      action: 'seek',
      timestamp: { $gte: startDate }
    });
    
    const completeCount = await Engagement.countDocuments({
      action: 'complete',
      timestamp: { $gte: startDate }
    });
    
    // Calculate time spent
    const timeSpentAggregation = await Engagement.aggregate([
      {
        $match: {
          action: 'timeSpent',
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$value' }
        }
      }
    ]);
    
    const timeSpentTotal = timeSpentAggregation.length > 0 ? timeSpentAggregation[0].total : 0;
    
    // Calculate unique video views for completion rate
    const uniqueVideoViews = await Engagement.aggregate([
      {
        $match: {
          action: 'play',
          timestamp: { $gte: startDate }
        }
      },
      {
        $group: {
          _id: '$video',
          count: { $sum: 1 }
        }
      },
      {
        $count: 'total'
      }
    ]);
    
    const totalUniqueViews = uniqueVideoViews.length > 0 ? uniqueVideoViews[0].total : 0;
    
    // Calculate average watch time
    const averageWatchTime = playCount > 0 ? timeSpentTotal / playCount : 0;
    
    // Calculate completion rate
    const completionRate = totalUniqueViews > 0 ? completeCount / totalUniqueViews : 0;
    
    return NextResponse.json({
      totalEngagements,
      playCount,
      pauseCount,
      seekCount,
      completeCount,
      timeSpentTotal,
      averageWatchTime,
      completionRate
    });
  } catch (error) {
    console.error('Error fetching engagement metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch engagement metrics' },
      { status: 500 }
    );
  }
}
