// This script creates initial categories in the database
const mongoose = require('mongoose');
require('dotenv').config({ path: '.env.local' });

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/adult-streaming';

// Define Category schema
const categorySchema = new mongoose.Schema(
  {
    name: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      maxlength: 50,
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    description: {
      type: String,
      required: true,
      trim: true,
      maxlength: 500,
    },
    thumbnailUrl: {
      type: String,
      required: true,
    },
    isActive: {
      type: Boolean,
      default: true,
    },
  },
  {
    timestamps: true,
  }
);

// We'll manually generate the slug instead of using a pre-save hook

// Create the Category model
const Category = mongoose.model('Category', categorySchema);

// Initial categories data
const initialCategories = [
  {
    name: 'Amateur',
    description: 'Real people, real passion. Amateur videos feature everyday individuals exploring their sexuality.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Amateur',
  },
  {
    name: 'MILF',
    description: 'Mature and experienced women showing their skills and passion.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=MILF',
  },
  {
    name: 'Lesbian',
    description: 'Women pleasuring women in intimate and passionate encounters.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Lesbian',
  },
  {
    name: 'Threesome',
    description: 'Three is never a crowd in these exciting multi-partner videos.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Threesome',
  },
  {
    name: 'Blonde',
    description: 'Videos featuring blonde performers in various scenarios.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Blonde',
  },
  {
    name: 'Anal',
    description: 'Exploring backdoor pleasures and anal adventures.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Anal',
  },
  {
    name: 'Asian',
    description: 'Videos featuring performers of Asian descent.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Asian',
  },
  {
    name: 'Big Tits',
    description: 'Videos featuring performers with large breasts.',
    thumbnailUrl: 'https://via.placeholder.com/640x360.png?text=Big+Tits',
  },
];

async function createCategories() {
  try {
    // Connect to MongoDB
    console.log(`Connecting to MongoDB at ${MONGODB_URI}...`);
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');

    // Create categories
    for (const categoryData of initialCategories) {
      // Generate slug from name
      const slug = categoryData.name
        .toLowerCase()
        .replace(/[^\w\s]/gi, '')
        .replace(/\s+/g, '-');

      // Check if category already exists
      const existingCategory = await Category.findOne({
        $or: [{ name: categoryData.name }, { slug: slug }]
      });

      if (existingCategory) {
        console.log(`Category "${categoryData.name}" already exists`);
      } else {
        // Create new category with slug
        const category = new Category({
          ...categoryData,
          slug: slug
        });
        await category.save();
        console.log(`Created category: ${category.name} (slug: ${category.slug})`);
      }
    }

    console.log('Categories creation completed');

    // Disconnect from MongoDB
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error creating categories:', error);
    process.exit(1);
  }
}

// Run the function
createCategories();
