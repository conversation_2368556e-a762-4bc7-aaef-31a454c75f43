import Link from 'next/link';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  return (
    <footer className="bg-gray-900 text-gray-300 py-8">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <h3 className="text-xl font-bold text-white mb-4">AdultTube</h3>
            <p className="text-sm">
              The best adult video streaming platform with thousands of high-quality videos.
            </p>
          </div>

          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Information</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/about" className="hover:text-red-500">
                  About Us
                </Link>
              </li>
              <li>
                <Link href="/contact" className="hover:text-red-500">
                  Contact
                </Link>
              </li>
              <li>
                <Link href="/faq" className="hover:text-red-500">
                  FAQ
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Legal</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/legal/terms" className="hover:text-red-500">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/legal/privacy" className="hover:text-red-500">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/legal/dmca" className="hover:text-red-500">
                  DMCA / Copyright
                </Link>
              </li>
              <li>
                <Link href="/legal/2257" className="hover:text-red-500">
                  18 U.S.C. 2257
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="text-lg font-semibold text-white mb-4">Support</h4>
            <ul className="space-y-2 text-sm">
              <li>
                <Link href="/legal/report" className="hover:text-red-500">
                  Report Content
                </Link>
              </li>
              <li>
                <Link href="/feedback" className="hover:text-red-500">
                  Feedback
                </Link>
              </li>
            </ul>
          </div>
        </div>

        <div className="mt-8 pt-6 border-t border-gray-800 text-center text-sm">
          <p>
            &copy; {currentYear} AdultTube. All rights reserved. This website is intended for adults only (18+).
          </p>
          <p className="mt-2">
            All models appearing on this website are 18 years or older. For 2257 compliance information, please contact the designated records custodian.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
