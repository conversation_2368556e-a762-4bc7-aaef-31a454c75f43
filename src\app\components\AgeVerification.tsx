'use client';

import { useState, useEffect } from 'react';

interface AgeVerificationProps {
  children: React.ReactNode;
}

const AgeVerification: React.FC<AgeVerificationProps> = ({ children }) => {
  const [isVerified, setIsVerified] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user has already verified their age
    // Only access localStorage on the client side
    if (typeof window !== 'undefined') {
      const verified = localStorage.getItem('age-verified');
      if (verified === 'true') {
        setIsVerified(true);
      }
    }
    setIsLoading(false);
  }, []);

  const handleVerify = () => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('age-verified', 'true');
      setIsVerified(true);
    }
  };

  const handleReject = () => {
    if (typeof window !== 'undefined') {
      window.location.href = 'https://www.google.com';
    }
  };

  if (isLoading) {
    return <div className="min-h-screen bg-gray-900 flex items-center justify-center">Loading...</div>;
  }

  if (!isVerified) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-50 p-4">
        <div className="bg-gray-800 rounded-lg p-8 max-w-md w-full text-center">
          <h2 className="text-2xl font-bold text-white mb-4">Age Verification</h2>

          <div className="mb-6">
            <svg className="w-16 h-16 mx-auto text-red-500" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd"></path>
            </svg>
          </div>

          <p className="text-white mb-6">
            This website contains adult content and is only suitable for people who are at least 18 years old.
          </p>

          <p className="text-white mb-8">
            By entering this site, you confirm that you are at least 18 years old or the legal age in your jurisdiction,
            whichever is greater, and agree to our Terms of Service and Privacy Policy.
          </p>

          <div className="flex flex-col sm:flex-row space-y-3 sm:space-y-0 sm:space-x-3">
            <button
              onClick={handleVerify}
              className="btn-primary flex-1"
            >
              I am 18 or older - Enter
            </button>

            <button
              onClick={handleReject}
              className="btn-secondary flex-1"
            >
              Leave
            </button>
          </div>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AgeVerification;
