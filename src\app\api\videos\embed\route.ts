import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
// Use the same models as the categories API
import Video from '@/app/models/Video';
import Category from '@/app/models/Category';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// Function to generate embed code from source URL
function generateEmbedCode(url: string, sourceSite: string): string {
  // Extract video ID from URL based on the source site
  let videoId = '';
  let embedCode = '';

  try {
    const urlObj = new URL(url);

    switch(sourceSite) {
      case 'xhamster':
        // Example: https://xhamster.com/videos/video-title-12345678
        videoId = url.split('/').pop()?.split('-').pop() || '';
        if (videoId) {
          embedCode = `<iframe src="https://xhamster.com/embed/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
        }
        break;

      case 'pornhub':
        // Example: https://www.pornhub.com/view_video.php?viewkey=ph123456789
        if (urlObj.searchParams.has('viewkey')) {
          videoId = urlObj.searchParams.get('viewkey') || '';
          embedCode = `<iframe src="https://www.pornhub.com/embed/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
        }
        break;

      case 'xvideos':
        // Example: https://www.xvideos.com/video12345678/video_title
        videoId = url.split('/')[3]?.replace('video', '') || '';
        if (videoId) {
          embedCode = `<iframe src="https://www.xvideos.com/embedframe/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
        }
        break;

      case 'redtube':
        // Example: https://www.redtube.com/12345678
        videoId = url.split('/').pop() || '';
        if (videoId && !isNaN(Number(videoId))) {
          embedCode = `<iframe src="https://embed.redtube.com/?id=${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
        }
        break;

      case 'youporn':
        // Example: https://www.youporn.com/watch/12345678/video-title/
        videoId = url.split('/watch/')[1]?.split('/')[0] || '';
        if (videoId) {
          embedCode = `<iframe src="https://www.youporn.com/embed/${videoId}" frameborder="0" width="640" height="360" scrolling="no" allowfullscreen></iframe>`;
        }
        break;

      default:
        // Generic fallback - create a link to the original content
        embedCode = `<div class="external-video"><p>This video is hosted on ${sourceSite}.</p><a href="${url}" target="_blank" rel="noopener noreferrer">Watch on ${sourceSite}</a></div>`;
    }

    return embedCode;
  } catch (error) {
    console.error('Error generating embed code:', error);
    return `<div class="external-video"><a href="${url}" target="_blank" rel="noopener noreferrer">Watch on ${sourceSite}</a></div>`;
  }
}

export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Parse the request body
    const data = await req.json();

    // Validate required fields
    const requiredFields = [
      'title',
      'description',
      'slug',
      'duration',
      'thumbnailUrl',
      'sourceUrl',
      'sourceSite',
      'categories'
    ];

    for (const field of requiredFields) {
      if (!data[field]) {
        return NextResponse.json(
          { error: `${field} is required` },
          { status: 400 }
        );
      }
    }

    // Validate categories
    if (!Array.isArray(data.categories) || data.categories.length === 0) {
      return NextResponse.json(
        { error: 'At least one category is required' },
        { status: 400 }
      );
    }

    // Check if categories exist
    for (const categoryId of data.categories) {
      const categoryExists = await Category.findById(categoryId);
      if (!categoryExists) {
        return NextResponse.json(
          { error: `Category with ID ${categoryId} does not exist` },
          { status: 400 }
        );
      }
    }

    // Generate embed code from source URL
    const embedCode = generateEmbedCode(data.sourceUrl, data.sourceSite);

    // If no embed code could be generated, return an error
    if (!embedCode) {
      return NextResponse.json(
        { error: 'Could not generate embed code from the provided URL' },
        { status: 400 }
      );
    }

    // Create a new video document
    const newVideo = new Video({
      title: data.title,
      description: data.description || '',
      slug: data.slug,
      duration: data.duration,
      views: 0,
      likes: 0,
      dislikes: 0,
      // Use thumbnail field for the @/app/models/Video model
      thumbnail: data.thumbnailUrl,
      // For embedded videos, we store the embed code in embedUrl
      // and the original source URL in sourceUrl
      embedUrl: data.embedUrl || embedCode, // Use provided embedUrl or generated embedCode
      videoUrl: data.embedUrl || embedCode, // Also set videoUrl for compatibility
      sourceUrl: data.sourceUrl,
      sourceSite: data.sourceSite,
      categories: data.categories,
      tags: data.tags || [],
      // Use a valid ObjectId from the session
      uploadedBy: (session.user as any).id,
      isPublished: data.isPublished !== undefined ? data.isPublished : true,
      isVerified: data.isVerified !== undefined ? data.isVerified : false,
      isActive: true
    });

    // Save the video to the database
    await newVideo.save();

    // Update video count for each category
    for (const categoryId of data.categories) {
      await Category.findByIdAndUpdate(
        categoryId,
        { $inc: { videoCount: 1 } }
      );
    }

    // Return success response
    return NextResponse.json(
      {
        success: true,
        message: 'Embedded video added successfully',
        video: {
          _id: newVideo._id,
          title: newVideo.title,
          slug: newVideo.slug
        }
      },
      { status: 201 }
    );
  } catch (error: any) {
    console.error('Error adding embedded video:', error);

    // Log more detailed error information
    if (error.name === 'ValidationError') {
      console.error('Validation Error Details:', error.errors);

      // Return specific validation error messages
      const validationErrors = Object.keys(error.errors).map(field => {
        return `${field}: ${error.errors[field].message}`;
      });

      return NextResponse.json(
        { error: `Validation failed: ${validationErrors.join(', ')}` },
        { status: 400 }
      );
    }

    // Handle duplicate slug error
    if (error.code === 11000 && error.keyPattern?.slug) {
      return NextResponse.json(
        { error: 'A video with this slug already exists' },
        { status: 400 }
      );
    }

    // Log the full error object for debugging
    console.error('Full error object:', JSON.stringify(error, null, 2));

    return NextResponse.json(
      { error: error.message || 'Failed to add embedded video' },
      { status: 500 }
    );
  }
}
