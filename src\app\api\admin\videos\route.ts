import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';
import Category from '@/app/models/Category';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '50'); // Higher limit for admin
    const search = searchParams.get('search');
    const category = searchParams.get('category');
    const status = searchParams.get('status');
    const sort = searchParams.get('sort') || 'newest';

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Connect to the database
    await dbConnect();

    // Build query - Admin can see all videos (including inactive)
    let query: any = {};

    // Add search filter if provided
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Add category filter if provided
    if (category && category !== 'all') {
      // Find category by name
      const categoryObj = await Category.findOne({ name: category });
      if (categoryObj) {
        query.categories = { $in: [categoryObj._id] };
      }
    }

    // Add status filter if provided
    if (status && status !== 'all') {
      if (status === 'active') {
        query.isActive = { $ne: false };
      } else if (status === 'inactive') {
        query.isActive = false;
      } else if (status === 'published') {
        query.isPublished = true;
      } else if (status === 'unpublished') {
        query.isPublished = false;
      }
    }

    // Build sort options
    let sortOptions: any = {};
    switch (sort) {
      case 'newest':
        sortOptions = { createdAt: -1 };
        break;
      case 'oldest':
        sortOptions = { createdAt: 1 };
        break;
      case 'most-viewed':
        sortOptions = { views: -1 };
        break;
      case 'most-liked':
        sortOptions = { likes: -1 };
        break;
      case 'title':
        sortOptions = { title: 1 };
        break;
      default:
        sortOptions = { createdAt: -1 };
    }

    // Execute query with pagination
    const videos = await Video.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .populate('categories', 'name slug')
      .populate('uploadedBy', 'username email')
      .lean();

    // Get total count for pagination
    const total = await Video.countDocuments(query);

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      videos,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching admin videos:', error);
    return NextResponse.json(
      { error: 'Failed to fetch videos' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(req.url);
    const videoId = searchParams.get('id');

    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Delete the video
    const deletedVideo = await Video.findByIdAndDelete(videoId);

    if (!deletedVideo) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: 'Video deleted successfully',
      video: deletedVideo
    });
  } catch (error) {
    console.error('Error deleting video:', error);
    return NextResponse.json(
      { error: 'Failed to delete video' },
      { status: 500 }
    );
  }
}
