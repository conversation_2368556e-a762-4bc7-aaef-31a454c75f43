'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaSearch, FaUser, FaBars, FaTimes, FaSignOutAlt, FaHeart, FaHistory } from 'react-icons/fa';
import { usePathname, useRouter } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    if (isUserMenuOpen) setIsUserMenuOpen(false);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
    if (isMenuOpen) setIsMenuOpen(false);
  };

  const handleLogout = async () => {
    await signOut({ redirect: false });
    router.push('/');
    router.refresh();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      if (typeof window !== 'undefined') {
        window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
      }
    }
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isUserMenuOpen) setIsUserMenuOpen(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isUserMenuOpen]);

  return (
    <header className="bg-gray-900 text-white shadow-md">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="text-2xl font-bold text-red-500">
            AdultTube
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-6">
            <Link href="/categories" className={`hover:text-red-500 ${pathname === '/categories' ? 'text-red-500' : ''}`}>
              Categories
            </Link>
            <Link href="/trending" className={`hover:text-red-500 ${pathname === '/trending' ? 'text-red-500' : ''}`}>
              Trending
            </Link>
            <Link href="/newest" className={`hover:text-red-500 ${pathname === '/newest' ? 'text-red-500' : ''}`}>
              Newest
            </Link>
          </nav>

          {/* Search Bar */}
          <div className="hidden md:block">
            <form onSubmit={handleSearch} className="flex items-center">
              <input
                type="text"
                placeholder="Search videos..."
                className="input-field"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button type="submit" className="ml-2 p-2 bg-red-500 rounded hover:bg-red-600">
                <FaSearch />
              </button>
            </form>
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {status === 'authenticated' && session ? (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleUserMenu();
                  }}
                  className="flex items-center hover:text-red-500"
                >
                  <FaUser className="inline mr-1" />
                  {(session.user as any).username || session.user?.name || 'Account'}
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-48 bg-gray-800 rounded-lg shadow-lg py-2 z-10">
                    <Link
                      href="/profile"
                      className="block px-4 py-2 hover:bg-gray-700 hover:text-red-500"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaUser className="inline mr-2" /> Profile
                    </Link>
                    <Link
                      href="/profile?tab=favorites"
                      className="block px-4 py-2 hover:bg-gray-700 hover:text-red-500"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaHeart className="inline mr-2" /> Favorites
                    </Link>
                    <Link
                      href="/profile?tab=history"
                      className="block px-4 py-2 hover:bg-gray-700 hover:text-red-500"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaHistory className="inline mr-2" /> History
                    </Link>
                    {(session.user as any).isAdmin && (
                      <Link
                        href="/admin"
                        className="block px-4 py-2 hover:bg-gray-700 hover:text-red-500"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <FaUser className="inline mr-2" /> Admin Panel
                      </Link>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleLogout();
                      }}
                      className="block w-full text-left px-4 py-2 hover:bg-gray-700 hover:text-red-500"
                    >
                      <FaSignOutAlt className="inline mr-2" /> Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link href="/auth/login" className="hover:text-red-500">
                <FaUser className="inline mr-1" /> Login
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden text-white" onClick={toggleMenu}>
            {isMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-700">
            <form onSubmit={handleSearch} className="mb-4">
              <div className="flex">
                <input
                  type="text"
                  placeholder="Search videos..."
                  className="input-field"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button type="submit" className="ml-2 p-2 bg-red-500 rounded hover:bg-red-600">
                  <FaSearch />
                </button>
              </div>
            </form>
            <nav className="flex flex-col space-y-3">
              <Link href="/categories" className="hover:text-red-500" onClick={toggleMenu}>
                Categories
              </Link>
              <Link href="/trending" className="hover:text-red-500" onClick={toggleMenu}>
                Trending
              </Link>
              <Link href="/newest" className="hover:text-red-500" onClick={toggleMenu}>
                Newest
              </Link>

              {status === 'authenticated' && session ? (
                <>
                  <Link href="/profile" className="hover:text-red-500" onClick={toggleMenu}>
                    <FaUser className="inline mr-2" /> Profile
                  </Link>
                  <Link href="/profile?tab=favorites" className="hover:text-red-500" onClick={toggleMenu}>
                    <FaHeart className="inline mr-2" /> Favorites
                  </Link>
                  <Link href="/profile?tab=history" className="hover:text-red-500" onClick={toggleMenu}>
                    <FaHistory className="inline mr-2" /> History
                  </Link>
                  {(session.user as any).isAdmin && (
                    <Link href="/admin" className="hover:text-red-500" onClick={toggleMenu}>
                      <FaUser className="inline mr-2" /> Admin Panel
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="text-left hover:text-red-500 w-full"
                  >
                    <FaSignOutAlt className="inline mr-2" /> Logout
                  </button>
                </>
              ) : (
                <Link href="/auth/login" className="hover:text-red-500" onClick={toggleMenu}>
                  <FaUser className="inline mr-2" /> Login
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
