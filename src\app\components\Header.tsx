'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaSearch, FaUser, FaBars, FaTimes, FaSignOutAlt, FaHeart, FaHistory, FaPlay, FaFire, FaClock, FaList } from 'react-icons/fa';
import { usePathname, useRouter } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    if (isUserMenuOpen) setIsUserMenuOpen(false);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
    if (isMenuOpen) setIsMenuOpen(false);
  };

  const handleLogout = async () => {
    await signOut({ redirect: false });
    router.push('/');
    router.refresh();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      if (typeof window !== 'undefined') {
        window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
      }
    }
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isUserMenuOpen) setIsUserMenuOpen(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isUserMenuOpen]);

  return (
    <header className="bg-gradient-to-r from-black via-gray-900 to-black text-white shadow-2xl border-b border-orange-500/30 sticky top-0 z-50 backdrop-blur-sm">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center py-4">
          {/* Logo */}
          <Link href="/" className="flex items-center group">
            <div className="bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-lg mr-3 group-hover:from-orange-600 group-hover:to-red-600 transition-all duration-300 shadow-lg shadow-orange-500/30">
              <FaPlay className="text-white text-xl" />
            </div>
            <span className="text-2xl font-bold bg-gradient-to-r from-orange-400 to-red-500 bg-clip-text text-transparent">
              AdultHub
            </span>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <Link
              href="/categories"
              className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transform hover:scale-105 ${
                pathname === '/categories' ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/30' : 'text-gray-300'
              }`}
            >
              <FaList className="mr-2" />
              Categories
            </Link>
            <Link
              href="/trending"
              className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transform hover:scale-105 ${
                pathname === '/trending' ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/30' : 'text-gray-300'
              }`}
            >
              <FaFire className="mr-2" />
              Trending
            </Link>
            <Link
              href="/newest"
              className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transform hover:scale-105 ${
                pathname === '/newest' ? 'bg-gradient-to-r from-orange-500 to-red-500 text-white shadow-lg shadow-orange-500/30' : 'text-gray-300'
              }`}
            >
              <FaClock className="mr-2" />
              Newest
            </Link>
          </nav>

          {/* Search Bar */}
          <div className="hidden md:block">
            <form onSubmit={handleSearch} className="flex items-center">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search videos..."
                  className="bg-gray-800 border border-gray-600 rounded-full px-4 py-2 pr-12 text-white placeholder-gray-400 focus:outline-none focus:border-orange-500 focus:ring-2 focus:ring-orange-500/30 transition-all duration-300 w-64"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button
                  type="submit"
                  className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-gradient-to-r from-orange-500 to-red-500 p-2 rounded-full hover:from-orange-600 hover:to-red-600 transition-all duration-300 shadow-lg shadow-orange-500/30"
                >
                  <FaSearch className="text-white" />
                </button>
              </div>
            </form>
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {status === 'authenticated' && session ? (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleUserMenu();
                  }}
                  className="flex items-center px-4 py-2 bg-gradient-to-r from-gray-700 to-gray-800 rounded-full hover:from-orange-500 hover:to-red-500 transition-all duration-300 transform hover:scale-105 shadow-lg"
                >
                  <div className="bg-gradient-to-r from-orange-500 to-red-500 p-1 rounded-full mr-2">
                    <FaUser className="text-white text-sm" />
                  </div>
                  <span className="text-white font-medium">
                    {(session.user as any).username || session.user?.name || 'Account'}
                  </span>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-gradient-to-br from-gray-800 to-gray-900 rounded-xl shadow-2xl py-2 z-10 border border-gray-700">
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-3 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transition-all duration-300 text-gray-300"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaUser className="mr-3 text-orange-400" /> Profile
                    </Link>
                    <Link
                      href="/profile?tab=favorites"
                      className="flex items-center px-4 py-3 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transition-all duration-300 text-gray-300"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaHeart className="mr-3 text-pink-400" /> Favorites
                    </Link>
                    <Link
                      href="/profile?tab=history"
                      className="flex items-center px-4 py-3 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transition-all duration-300 text-gray-300"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaHistory className="mr-3 text-blue-400" /> History
                    </Link>
                    {(session.user as any).isAdmin && (
                      <Link
                        href="/admin"
                        className="flex items-center px-4 py-3 hover:bg-gradient-to-r hover:from-orange-500 hover:to-red-500 hover:text-white transition-all duration-300 text-gray-300"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <FaUser className="mr-3 text-green-400" /> Admin Panel
                      </Link>
                    )}
                    <div className="border-t border-gray-700 my-2"></div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleLogout();
                      }}
                      className="flex items-center w-full px-4 py-3 hover:bg-gradient-to-r hover:from-red-500 hover:to-red-600 hover:text-white transition-all duration-300 text-gray-300"
                    >
                      <FaSignOutAlt className="mr-3 text-red-400" /> Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href="/auth/login"
                className="flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 rounded-full hover:from-orange-600 hover:to-red-600 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-orange-500/30 text-white font-medium"
              >
                <FaUser className="mr-2" /> Login
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button className="md:hidden text-white" onClick={toggleMenu}>
            {isMenuOpen ? <FaTimes size={24} /> : <FaBars size={24} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-gray-700">
            <form onSubmit={handleSearch} className="mb-4">
              <div className="flex">
                <input
                  type="text"
                  placeholder="Search videos..."
                  className="input-field"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button type="submit" className="ml-2 p-2 bg-red-500 rounded hover:bg-red-600">
                  <FaSearch />
                </button>
              </div>
            </form>
            <nav className="flex flex-col space-y-3">
              <Link href="/categories" className="hover:text-red-500" onClick={toggleMenu}>
                Categories
              </Link>
              <Link href="/trending" className="hover:text-red-500" onClick={toggleMenu}>
                Trending
              </Link>
              <Link href="/newest" className="hover:text-red-500" onClick={toggleMenu}>
                Newest
              </Link>

              {status === 'authenticated' && session ? (
                <>
                  <Link href="/profile" className="hover:text-red-500" onClick={toggleMenu}>
                    <FaUser className="inline mr-2" /> Profile
                  </Link>
                  <Link href="/profile?tab=favorites" className="hover:text-red-500" onClick={toggleMenu}>
                    <FaHeart className="inline mr-2" /> Favorites
                  </Link>
                  <Link href="/profile?tab=history" className="hover:text-red-500" onClick={toggleMenu}>
                    <FaHistory className="inline mr-2" /> History
                  </Link>
                  {(session.user as any).isAdmin && (
                    <Link href="/admin" className="hover:text-red-500" onClick={toggleMenu}>
                      <FaUser className="inline mr-2" /> Admin Panel
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="text-left hover:text-red-500 w-full"
                  >
                    <FaSignOutAlt className="inline mr-2" /> Logout
                  </button>
                </>
              ) : (
                <Link href="/auth/login" className="hover:text-red-500" onClick={toggleMenu}>
                  <FaUser className="inline mr-2" /> Login
                </Link>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
