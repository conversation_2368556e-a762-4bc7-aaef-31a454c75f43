'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaSearch, FaUser, FaBars, FaTimes, FaSignOutAlt, FaHeart, FaHistory, FaPlay, FaFire, FaClock, FaList, FaHome, FaTrendingUp } from 'react-icons/fa';
import { usePathname, useRouter } from 'next/navigation';
import { useSession, signOut } from 'next-auth/react';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
    if (isUserMenuOpen) setIsUserMenuOpen(false);
  };

  const toggleUserMenu = () => {
    setIsUserMenuOpen(!isUserMenuOpen);
    if (isMenuOpen) setIsMenuOpen(false);
  };

  const handleLogout = async () => {
    await signOut({ redirect: false });
    router.push('/');
    router.refresh();
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      if (typeof window !== 'undefined') {
        window.location.href = `/search?q=${encodeURIComponent(searchQuery)}`;
      }
    }
  };

  // Close user menu when clicking outside
  useEffect(() => {
    const handleClickOutside = () => {
      if (isUserMenuOpen) setIsUserMenuOpen(false);
    };

    document.addEventListener('click', handleClickOutside);
    return () => document.removeEventListener('click', handleClickOutside);
  }, [isUserMenuOpen]);

  return (
    <header className="bg-ph-black-950 text-white shadow-lg sticky top-0 z-50 border-b border-ph-gray-800">
      <div className="container-ph">
        <div className="flex justify-between items-center py-3">
          {/* Logo - Pornhub style */}
          <Link href="/" className="flex items-center group">
            <div className="flex items-center">
              <span className="text-2xl font-bold text-white bg-ph-black-950 px-3 py-1 rounded-l-md">
                Adult
              </span>
              <span className="text-2xl font-bold text-ph-black-950 bg-ph-orange-500 px-3 py-1 rounded-r-md group-hover:bg-ph-orange-600 transition-colors duration-200">
                Hub
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden lg:flex items-center space-x-6">
            <Link
              href="/"
              className={`flex items-center px-3 py-2 text-sm font-medium transition-colors duration-200 hover:text-ph-orange-500 ${
                pathname === '/' ? 'text-ph-orange-500' : 'text-ph-gray-300'
              }`}
            >
              <FaHome className="mr-2" />
              Home
            </Link>
            <Link
              href="/categories"
              className={`flex items-center px-3 py-2 text-sm font-medium transition-colors duration-200 hover:text-ph-orange-500 ${
                pathname === '/categories' ? 'text-ph-orange-500' : 'text-ph-gray-300'
              }`}
            >
              <FaList className="mr-2" />
              Categories
            </Link>
            <Link
              href="/trending"
              className={`flex items-center px-3 py-2 text-sm font-medium transition-colors duration-200 hover:text-ph-orange-500 ${
                pathname === '/trending' ? 'text-ph-orange-500' : 'text-ph-gray-300'
              }`}
            >
              <FaTrendingUp className="mr-2" />
              Trending
            </Link>
            <Link
              href="/newest"
              className={`flex items-center px-3 py-2 text-sm font-medium transition-colors duration-200 hover:text-ph-orange-500 ${
                pathname === '/newest' ? 'text-ph-orange-500' : 'text-ph-gray-300'
              }`}
            >
              <FaClock className="mr-2" />
              Newest
            </Link>
          </nav>

          {/* Search Bar */}
          <div className="hidden md:block flex-1 max-w-md mx-6">
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                placeholder="Search videos..."
                className="input-ph w-full pr-12 text-sm"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button
                type="submit"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-ph-gray-400 hover:text-ph-orange-500 transition-colors duration-200"
              >
                <FaSearch className="text-lg" />
              </button>
            </form>
          </div>

          {/* User Menu */}
          <div className="hidden md:flex items-center space-x-4">
            {status === 'authenticated' && session ? (
              <div className="relative">
                <button
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleUserMenu();
                  }}
                  className="flex items-center px-4 py-2 bg-ph-gray-800 hover:bg-ph-gray-700 rounded-md transition-colors duration-200"
                >
                  <div className="w-8 h-8 bg-ph-orange-500 rounded-full flex items-center justify-center mr-3">
                    <FaUser className="text-white text-sm" />
                  </div>
                  <span className="text-white font-medium text-sm">
                    {(session.user as any).username || session.user?.name || 'Account'}
                  </span>
                </button>

                {isUserMenuOpen && (
                  <div className="absolute right-0 mt-2 w-56 bg-ph-gray-900 rounded-lg shadow-xl py-2 z-10 border border-ph-gray-700">
                    <Link
                      href="/profile"
                      className="flex items-center px-4 py-3 hover:bg-ph-gray-800 transition-colors duration-200 text-ph-gray-300 hover:text-white"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaUser className="mr-3 text-ph-orange-500" /> Profile
                    </Link>
                    <Link
                      href="/profile?tab=favorites"
                      className="flex items-center px-4 py-3 hover:bg-ph-gray-800 transition-colors duration-200 text-ph-gray-300 hover:text-white"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaHeart className="mr-3 text-ph-orange-500" /> Favorites
                    </Link>
                    <Link
                      href="/profile?tab=history"
                      className="flex items-center px-4 py-3 hover:bg-ph-gray-800 transition-colors duration-200 text-ph-gray-300 hover:text-white"
                      onClick={() => setIsUserMenuOpen(false)}
                    >
                      <FaHistory className="mr-3 text-ph-orange-500" /> History
                    </Link>
                    {(session.user as any).isAdmin && (
                      <Link
                        href="/admin"
                        className="flex items-center px-4 py-3 hover:bg-ph-gray-800 transition-colors duration-200 text-ph-gray-300 hover:text-white"
                        onClick={() => setIsUserMenuOpen(false)}
                      >
                        <FaUser className="mr-3 text-ph-orange-500" /> Admin Panel
                      </Link>
                    )}
                    <div className="border-t border-ph-gray-700 my-2"></div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleLogout();
                      }}
                      className="flex items-center w-full px-4 py-3 hover:bg-ph-gray-800 transition-colors duration-200 text-ph-gray-300 hover:text-white"
                    >
                      <FaSignOutAlt className="mr-3 text-red-400" /> Logout
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <Link
                href="/auth/login"
                className="btn-ph-primary text-sm"
              >
                <FaUser className="mr-2" /> Login
              </Link>
            )}
          </div>

          {/* Mobile Menu Button */}
          <button
            className="lg:hidden text-white p-2 hover:text-ph-orange-500 transition-colors duration-200"
            onClick={toggleMenu}
          >
            {isMenuOpen ? <FaTimes size={20} /> : <FaBars size={20} />}
          </button>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="lg:hidden py-4 border-t border-ph-gray-800">
            {/* Mobile Search */}
            <form onSubmit={handleSearch} className="mb-6">
              <div className="relative">
                <input
                  type="text"
                  placeholder="Search videos..."
                  className="input-ph w-full pr-12"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
                <button
                  type="submit"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-ph-gray-400 hover:text-ph-orange-500 transition-colors duration-200"
                >
                  <FaSearch />
                </button>
              </div>
            </form>

            {/* Mobile Navigation */}
            <nav className="space-y-1">
              <Link
                href="/"
                className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                onClick={toggleMenu}
              >
                <FaHome className="mr-3" /> Home
              </Link>
              <Link
                href="/categories"
                className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                onClick={toggleMenu}
              >
                <FaList className="mr-3" /> Categories
              </Link>
              <Link
                href="/trending"
                className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                onClick={toggleMenu}
              >
                <FaTrendingUp className="mr-3" /> Trending
              </Link>
              <Link
                href="/newest"
                className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                onClick={toggleMenu}
              >
                <FaClock className="mr-3" /> Newest
              </Link>

              {/* Mobile User Menu */}
              {status === 'authenticated' && session ? (
                <>
                  <div className="border-t border-ph-gray-800 my-3"></div>
                  <Link
                    href="/profile"
                    className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                    onClick={toggleMenu}
                  >
                    <FaUser className="mr-3" /> Profile
                  </Link>
                  <Link
                    href="/profile?tab=favorites"
                    className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                    onClick={toggleMenu}
                  >
                    <FaHeart className="mr-3" /> Favorites
                  </Link>
                  <Link
                    href="/profile?tab=history"
                    className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                    onClick={toggleMenu}
                  >
                    <FaHistory className="mr-3" /> History
                  </Link>
                  {(session.user as any).isAdmin && (
                    <Link
                      href="/admin"
                      className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                      onClick={toggleMenu}
                    >
                      <FaUser className="mr-3" /> Admin Panel
                    </Link>
                  )}
                  <button
                    onClick={handleLogout}
                    className="flex items-center w-full px-3 py-3 text-ph-gray-300 hover:text-red-400 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                  >
                    <FaSignOutAlt className="mr-3" /> Logout
                  </button>
                </>
              ) : (
                <>
                  <div className="border-t border-ph-gray-800 my-3"></div>
                  <Link
                    href="/auth/login"
                    className="flex items-center px-3 py-3 text-ph-gray-300 hover:text-ph-orange-500 hover:bg-ph-gray-800 rounded-md transition-colors duration-200"
                    onClick={toggleMenu}
                  >
                    <FaUser className="mr-3" /> Login
                  </Link>
                </>
              )}
            </nav>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
