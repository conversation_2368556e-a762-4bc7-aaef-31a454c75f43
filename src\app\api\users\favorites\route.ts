import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import User from '@/app/models/User';
import Video from '@/app/models/Video';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// GET /api/users/favorites - Get user favorites
export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get user with populated favorites
    const user = await User.findById((session.user as any).id)
      .populate({
        path: 'favorites',
        select: 'title thumbnail duration views slug categories',
        populate: {
          path: 'categories',
          select: 'name slug'
        }
      });
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(user.favorites);
  } catch (error: any) {
    console.error('Error fetching favorites:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch favorites' },
      { status: 500 }
    );
  }
}

// POST /api/users/favorites - Add video to favorites
export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { videoId } = await req.json();
    
    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }
    
    // Check if video exists
    const video = await Video.findById(videoId);
    
    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }
    
    // Add to favorites if not already added
    const user = await User.findByIdAndUpdate(
      (session.user as any).id,
      { $addToSet: { favorites: videoId } },
      { new: true }
    );
    
    return NextResponse.json({ success: true, message: 'Added to favorites' });
  } catch (error: any) {
    console.error('Error adding to favorites:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to add to favorites' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/favorites - Remove video from favorites
export async function DELETE(req: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { searchParams } = new URL(req.url);
    const videoId = searchParams.get('videoId');
    
    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }
    
    // Remove from favorites
    await User.findByIdAndUpdate(
      (session.user as any).id,
      { $pull: { favorites: videoId } }
    );
    
    return NextResponse.json({ success: true, message: 'Removed from favorites' });
  } catch (error: any) {
    console.error('Error removing from favorites:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to remove from favorites' },
      { status: 500 }
    );
  }
}
