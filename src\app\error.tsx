'use client';

import { useEffect } from 'react';
import Link from 'next/link';
import { FaHome, FaRedo, FaExclamationCircle } from 'react-icons/fa';

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error('Application error:', error);
  }, [error]);

  return (
    <div className="min-h-screen bg-ph-black-950 flex items-center justify-center">
      <div className="container-ph">
        <div className="text-center">
          {/* Error Icon */}
          <div className="mb-8">
            <FaExclamationCircle className="text-red-500 text-8xl mx-auto mb-4" />
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-4">
              Something went wrong!
            </h1>
            <h2 className="text-xl md:text-2xl font-semibold text-ph-gray-300 mb-6">
              We encountered an unexpected error
            </h2>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <p className="text-lg text-ph-gray-400 mb-4 max-w-2xl mx-auto">
              We're sorry, but something went wrong on our end. This error has been logged and we're working to fix it.
            </p>
            <p className="text-ph-gray-500 max-w-xl mx-auto">
              You can try refreshing the page or go back to the homepage to continue browsing.
            </p>
            
            {/* Show error details in development */}
            {process.env.NODE_ENV === 'development' && (
              <div className="mt-6 p-4 bg-ph-gray-800 rounded-lg text-left max-w-2xl mx-auto">
                <h3 className="text-red-400 font-semibold mb-2">Error Details (Development Only):</h3>
                <pre className="text-ph-gray-300 text-sm overflow-auto">
                  {error.message}
                </pre>
                {error.digest && (
                  <p className="text-ph-gray-400 text-xs mt-2">
                    Error ID: {error.digest}
                  </p>
                )}
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <button
              onClick={reset}
              className="btn-ph-primary flex items-center"
            >
              <FaRedo className="mr-2" />
              Try Again
            </button>
            
            <Link
              href="/"
              className="btn-ph-secondary flex items-center"
            >
              <FaHome className="mr-2" />
              Go to Homepage
            </Link>
          </div>

          {/* Help Text */}
          <div className="mt-12">
            <p className="text-ph-gray-500 text-sm">
              If this problem persists, please contact our support team.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
