import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import User from '@/app/models/User';
import Video from '@/app/models/Video';
import mongoose from 'mongoose';

export async function GET(req: NextRequest) {
  try {
    // Connect to the database
    await dbConnect();
    
    const { searchParams } = new URL(req.url);
    const limit = parseInt(searchParams.get('limit') || '12');
    
    // Get user session
    const session = await getServerSession(authOptions);
    
    // If user is logged in, provide personalized recommendations
    if (session && session.user) {
      const userId = (session.user as any).id;
      
      // Find user and populate watch history
      const user = await User.findById(userId);
      
      if (!user) {
        return NextResponse.json(
          { error: 'User not found' },
          { status: 404 }
        );
      }
      
      // If user has watch history, use it for recommendations
      if (user.watchHistory && user.watchHistory.length > 0) {
        // Get the categories and tags from the user's recently watched videos
        const recentlyWatched = await Video.find({
          _id: { $in: user.watchHistory.slice(0, 10) }
        });
        
        // Extract categories and tags from recently watched videos
        const categoryIds = new Set<string>();
        const tags = new Set<string>();
        
        recentlyWatched.forEach(video => {
          // Add categories
          if (video.categories && video.categories.length > 0) {
            video.categories.forEach((catId: any) => categoryIds.add(catId.toString()));
          }
          
          // Add tags
          if (video.tags && video.tags.length > 0) {
            video.tags.forEach((tag: string) => tags.add(tag));
          }
        });
        
        // Convert sets to arrays
        const categoryIdsArray = Array.from(categoryIds);
        const tagsArray = Array.from(tags);
        
        // Find videos with similar categories or tags, excluding already watched videos
        const recommendedVideos = await Video.aggregate([
          {
            $match: {
              $and: [
                { isPublished: true },
                { _id: { $nin: user.watchHistory } } // Exclude watched videos
              ],
              $or: [
                { categories: { $in: categoryIdsArray.map(id => new mongoose.Types.ObjectId(id)) } },
                { tags: { $in: tagsArray } }
              ]
            }
          },
          // Add a score based on matching categories and tags
          {
            $addFields: {
              categoryMatchCount: {
                $size: {
                  $setIntersection: [
                    "$categories",
                    categoryIdsArray.map(id => new mongoose.Types.ObjectId(id))
                  ]
                }
              },
              tagMatchCount: {
                $size: {
                  $setIntersection: ["$tags", tagsArray]
                }
              }
            }
          },
          // Calculate a recommendation score
          {
            $addFields: {
              recommendationScore: {
                $add: [
                  { $multiply: ["$categoryMatchCount", 3] }, // Categories are weighted more
                  { $multiply: ["$tagMatchCount", 1] },
                  { $multiply: ["$views", 0.0001] }, // Consider popularity
                  { $multiply: ["$likes", 0.001] }   // Consider likes
                ]
              }
            }
          },
          { $sort: { recommendationScore: -1 } },
          { $limit: limit },
          {
            $project: {
              _id: 1,
              title: 1,
              thumbnailUrl: 1,
              duration: 1,
              views: 1,
              likes: 1,
              slug: 1,
              createdAt: 1,
              recommendationScore: 1
            }
          }
        ]);
        
        return NextResponse.json({
          recommendations: recommendedVideos,
          type: 'personalized'
        });
      }
    }
    
    // Fallback to trending videos if user is not logged in or has no watch history
    const trendingVideos = await Video.find({ isPublished: true })
      .sort({ views: -1, likes: -1 })
      .limit(limit)
      .select('_id title thumbnailUrl duration views likes slug createdAt');
    
    return NextResponse.json({
      recommendations: trendingVideos,
      type: 'trending'
    });
    
  } catch (error) {
    console.error('Error generating recommendations:', error);
    return NextResponse.json(
      { error: 'Failed to generate recommendations' },
      { status: 500 }
    );
  }
}
