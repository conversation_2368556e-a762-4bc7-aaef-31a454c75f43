'use client';

import { useState } from 'react';
import { FaPlus, FaEdit, FaTrash, FaEye, FaEyeSlash, FaImage } from 'react-icons/fa';

interface Ad {
  _id: string;
  title: string;
  description: string;
  imageUrl: string;
  targetUrl: string;
  position: 'header' | 'sidebar' | 'footer' | 'between-videos';
  isActive: boolean;
  impressions: number;
  clicks: number;
  createdAt: string;
}

export default function AdsAdmin() {
  const [ads, setAds] = useState<Ad[]>([
    {
      _id: '1',
      title: 'Sample Banner Ad',
      description: 'This is a sample advertisement',
      imageUrl: 'https://via.placeholder.com/728x90?text=Sample+Ad',
      targetUrl: 'https://example.com',
      position: 'header',
      isActive: true,
      impressions: 1250,
      clicks: 45,
      createdAt: new Date().toISOString()
    }
  ]);
  const [showModal, setShowModal] = useState(false);
  const [editingAd, setEditingAd] = useState<Ad | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    imageUrl: '',
    targetUrl: '',
    position: 'header' as const,
    isActive: true
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (editingAd) {
      // Update existing ad
      setAds(prev => prev.map(ad => 
        ad._id === editingAd._id 
          ? { ...ad, ...formData }
          : ad
      ));
    } else {
      // Create new ad
      const newAd: Ad = {
        _id: Date.now().toString(),
        ...formData,
        impressions: 0,
        clicks: 0,
        createdAt: new Date().toISOString()
      };
      setAds(prev => [...prev, newAd]);
    }
    
    setShowModal(false);
    setEditingAd(null);
    setFormData({
      title: '',
      description: '',
      imageUrl: '',
      targetUrl: '',
      position: 'header',
      isActive: true
    });
  };

  const handleEdit = (ad: Ad) => {
    setEditingAd(ad);
    setFormData({
      title: ad.title,
      description: ad.description,
      imageUrl: ad.imageUrl,
      targetUrl: ad.targetUrl,
      position: ad.position,
      isActive: ad.isActive
    });
    setShowModal(true);
  };

  const handleDelete = (adId: string) => {
    if (confirm('Are you sure you want to delete this ad?')) {
      setAds(prev => prev.filter(ad => ad._id !== adId));
    }
  };

  const toggleActive = (adId: string) => {
    setAds(prev => prev.map(ad => 
      ad._id === adId 
        ? { ...ad, isActive: !ad.isActive }
        : ad
    ));
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const calculateCTR = (clicks: number, impressions: number) => {
    if (impressions === 0) return '0.00';
    return ((clicks / impressions) * 100).toFixed(2);
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Advertisements Management</h1>
        <button
          onClick={() => {
            setEditingAd(null);
            setFormData({
              title: '',
              description: '',
              imageUrl: '',
              targetUrl: '',
              position: 'header',
              isActive: true
            });
            setShowModal(true);
          }}
          className="btn-primary flex items-center"
        >
          <FaPlus className="mr-2" /> Add Advertisement
        </button>
      </div>

      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Advertisement
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Performance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {ads.map((ad) => (
                <tr key={ad._id} className="hover:bg-gray-700">
                  <td className="px-6 py-4">
                    <div className="flex items-start space-x-3">
                      <div className="w-16 h-12 bg-gray-600 rounded flex items-center justify-center overflow-hidden">
                        {ad.imageUrl ? (
                          <img src={ad.imageUrl} alt={ad.title} className="w-full h-full object-cover" />
                        ) : (
                          <FaImage className="text-gray-400" />
                        )}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-white">{ad.title}</div>
                        <div className="text-sm text-gray-400 max-w-xs truncate">{ad.description}</div>
                        <div className="text-xs text-gray-500 mt-1">
                          Target: <a href={ad.targetUrl} target="_blank" rel="noopener noreferrer" className="text-blue-400 hover:underline">
                            {ad.targetUrl}
                          </a>
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800">
                      {ad.position.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-white">
                      <div>Impressions: {ad.impressions.toLocaleString()}</div>
                      <div>Clicks: {ad.clicks.toLocaleString()}</div>
                      <div>CTR: {calculateCTR(ad.clicks, ad.impressions)}%</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      ad.isActive 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {ad.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => toggleActive(ad._id)}
                        className={`${
                          ad.isActive ? 'text-yellow-400 hover:text-yellow-300' : 'text-green-400 hover:text-green-300'
                        }`}
                        title={ad.isActive ? 'Deactivate' : 'Activate'}
                      >
                        {ad.isActive ? <FaEyeSlash /> : <FaEye />}
                      </button>
                      <button
                        onClick={() => handleEdit(ad)}
                        className="text-blue-400 hover:text-blue-300"
                        title="Edit"
                      >
                        <FaEdit />
                      </button>
                      <button
                        onClick={() => handleDelete(ad._id)}
                        className="text-red-400 hover:text-red-300"
                        title="Delete"
                      >
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-lg mx-4 max-h-[90vh] overflow-y-auto">
            <h2 className="text-xl font-bold text-white mb-4">
              {editingAd ? 'Edit Advertisement' : 'Add Advertisement'}
            </h2>
            
            <form onSubmit={handleSubmit}>
              <div className="mb-4">
                <label className="block text-white mb-2">Title *</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                  className="input-field w-full"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-white mb-2">Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  className="input-field w-full h-24"
                  rows={3}
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-white mb-2">Image URL *</label>
                <input
                  type="url"
                  value={formData.imageUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, imageUrl: e.target.value }))}
                  className="input-field w-full"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-white mb-2">Target URL *</label>
                <input
                  type="url"
                  value={formData.targetUrl}
                  onChange={(e) => setFormData(prev => ({ ...prev, targetUrl: e.target.value }))}
                  className="input-field w-full"
                  required
                />
              </div>
              
              <div className="mb-4">
                <label className="block text-white mb-2">Position *</label>
                <select
                  value={formData.position}
                  onChange={(e) => setFormData(prev => ({ ...prev, position: e.target.value as any }))}
                  className="input-field w-full"
                  required
                >
                  <option value="header">Header</option>
                  <option value="sidebar">Sidebar</option>
                  <option value="footer">Footer</option>
                  <option value="between-videos">Between Videos</option>
                </select>
              </div>
              
              <div className="mb-6">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={formData.isActive}
                    onChange={(e) => setFormData(prev => ({ ...prev, isActive: e.target.checked }))}
                    className="mr-2"
                  />
                  <span className="text-white">Active</span>
                </label>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={() => setShowModal(false)}
                  className="btn-secondary"
                >
                  Cancel
                </button>
                <button type="submit" className="btn-primary">
                  {editingAd ? 'Update' : 'Create'}
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  );
}
