import mongoose, { Schema, Document } from 'mongoose';

export interface IComment extends Document {
  content: string;
  video: mongoose.Types.ObjectId;
  user: mongoose.Types.ObjectId;
  likes: number;
  dislikes: number;
  isApproved: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const CommentSchema: Schema = new Schema(
  {
    content: {
      type: String,
      required: [true, 'Comment content is required'],
      trim: true,
      maxlength: [1000, 'Comment cannot be more than 1000 characters'],
    },
    video: {
      type: Schema.Types.ObjectId,
      ref: 'Video',
      required: [true, 'Video reference is required'],
    },
    user: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'User reference is required'],
    },
    likes: {
      type: Number,
      default: 0,
    },
    dislikes: {
      type: Number,
      default: 0,
    },
    isApproved: {
      type: Boolean,
      default: true, // Auto-approve comments by default, can be changed for moderation
    },
  },
  {
    timestamps: true,
  }
);

// Check if model exists before creating a new one (for Next.js hot reloading)
export default mongoose.models.Comment || mongoose.model<IComment>('Comment', CommentSchema);
