@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --primary: #ef4444;
  --secondary: #000000;
  --background: #0f0f0f;
  --foreground: #ffffff;
  --accent: #ef4444;
  --text-light: #cccccc;
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

@layer components {
  .btn-primary {
    @apply bg-red-500 hover:bg-red-600 text-white font-bold py-2 px-4 rounded;
  }

  .btn-secondary {
    @apply bg-gray-800 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded;
  }

  .card {
    @apply bg-gray-900 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300;
  }

  .input-field {
    @apply bg-gray-800 text-white rounded px-4 py-2 w-full focus:outline-none focus:ring-2 focus:ring-red-500;
  }
}
