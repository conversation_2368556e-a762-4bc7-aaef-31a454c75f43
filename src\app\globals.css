@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --ph-orange: #f97316;
  --ph-orange-dark: #ea580c;
  --ph-black: #000000;
  --ph-gray-dark: #1a1a1a;
  --ph-gray: #2a2a2a;
  --ph-gray-light: #3a3a3a;
  --ph-text: #ffffff;
  --ph-text-muted: #cccccc;
  --ph-text-light: #999999;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--ph-black);
  color: var(--ph-text);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Pornhub-style scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--ph-gray-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--ph-orange);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ph-orange-dark);
}

@layer components {
  /* Pornhub-style buttons */
  .btn-ph-primary {
    @apply bg-ph-orange-500 hover:bg-ph-orange-600 text-white font-semibold py-2.5 px-6 rounded-md transition-all duration-200 transform hover:scale-105 active:scale-95;
  }

  .btn-ph-secondary {
    @apply bg-ph-gray-700 hover:bg-ph-gray-600 text-white font-semibold py-2.5 px-6 rounded-md transition-all duration-200;
  }

  .btn-ph-outline {
    @apply border-2 border-ph-orange-500 text-ph-orange-500 hover:bg-ph-orange-500 hover:text-white font-semibold py-2 px-6 rounded-md transition-all duration-200;
  }

  /* Pornhub-style cards */
  .card-ph {
    @apply bg-ph-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-ph-gray-700;
  }

  .card-ph-hover {
    @apply transform hover:scale-105 transition-transform duration-200;
  }

  /* Pornhub-style inputs */
  .input-ph {
    @apply bg-ph-gray-800 text-white rounded-md px-4 py-3 w-full border border-ph-gray-600 focus:outline-none focus:ring-2 focus:ring-ph-orange-500 focus:border-transparent transition-all duration-200;
  }

  .input-ph::placeholder {
    @apply text-ph-gray-400;
  }

  /* Admin form inputs - ensure black text on light background */
  .input-field {
    @apply bg-gray-100 text-black rounded-md px-4 py-3 w-full border border-gray-300 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent transition-all duration-200;
  }

  .input-field::placeholder {
    @apply text-gray-500;
  }

  /* Pornhub-style text styles */
  .text-ph-primary {
    @apply text-ph-orange-500;
  }

  .text-ph-muted {
    @apply text-ph-gray-400;
  }

  .text-ph-light {
    @apply text-ph-gray-300;
  }

  /* Video player styles */
  .video-player-container {
    @apply relative w-full bg-black rounded-lg overflow-hidden;
    aspect-ratio: 16/9;
  }

  .video-thumbnail {
    @apply absolute inset-0 w-full h-full object-cover;
  }

  .video-overlay {
    @apply absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200;
  }

  .play-button {
    @apply w-16 h-16 bg-ph-orange-500 rounded-full flex items-center justify-center text-white text-2xl hover:bg-ph-orange-600 transition-colors duration-200;
  }

  /* Mobile-first responsive utilities */
  .container-ph {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .grid-ph-videos {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6;
  }

  .grid-ph-categories {
    @apply grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4;
  }

  /* Enhanced mobile optimizations */
  .btn-ph-primary,
  .btn-ph-secondary {
    @apply min-h-[44px] min-w-[44px];
    touch-action: manipulation;
  }

  /* Mobile-first video grid */
  .video-grid-mobile {
    @apply grid gap-3 sm:gap-4 md:gap-6;
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  }

  @media (min-width: 640px) {
    .video-grid-mobile {
      grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    }
  }

  /* Mobile navigation improvements */
  .mobile-nav {
    @apply fixed bottom-0 left-0 right-0 bg-ph-black-900 border-t border-ph-gray-700 z-50;
    padding-bottom: env(safe-area-inset-bottom);
  }

  /* Improved touch scrolling */
  .scroll-smooth-mobile {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
    overscroll-behavior: contain;
  }

  /* Mobile video player optimizations */
  .video-player-mobile {
    @apply w-full;
    aspect-ratio: 16/9;
    max-height: 50vh;
  }

  /* Mobile category chips */
  .category-chip-mobile {
    @apply px-3 py-2 text-sm whitespace-nowrap bg-ph-gray-700 hover:bg-ph-orange-500 rounded-full transition-colors;
    min-width: fit-content;
  }

  /* Mobile-optimized text sizes */
  .text-mobile-lg {
    @apply text-lg sm:text-xl md:text-2xl lg:text-3xl;
  }

  .text-mobile-base {
    @apply text-sm sm:text-base md:text-lg;
  }

  /* Touch-friendly spacing */
  .spacing-mobile {
    @apply p-3 sm:p-4 md:p-6;
  }

  .gap-mobile {
    @apply gap-3 sm:gap-4 md:gap-6;
  }

  /* Modern custom scrollbar */
  .custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: rgba(249, 115, 22, 0.5) rgba(55, 65, 81, 0.3);
  }

  .custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: rgba(55, 65, 81, 0.3);
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, rgba(249, 115, 22, 0.6), rgba(234, 88, 12, 0.6));
    border-radius: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, rgba(249, 115, 22, 0.8), rgba(234, 88, 12, 0.8));
  }

  /* Modern glass effect */
  .glass-effect {
    backdrop-filter: blur(16px);
    -webkit-backdrop-filter: blur(16px);
    background: rgba(31, 41, 55, 0.4);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  /* Smooth animations */
  .smooth-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .smooth-hover:hover {
    transform: translateY(-2px);
  }
}
