@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --ph-orange: #f97316;
  --ph-orange-dark: #ea580c;
  --ph-black: #000000;
  --ph-gray-dark: #1a1a1a;
  --ph-gray: #2a2a2a;
  --ph-gray-light: #3a3a3a;
  --ph-text: #ffffff;
  --ph-text-muted: #cccccc;
  --ph-text-light: #999999;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
}

body {
  background-color: var(--ph-black);
  color: var(--ph-text);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Pornhub-style scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--ph-gray-dark);
}

::-webkit-scrollbar-thumb {
  background: var(--ph-orange);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--ph-orange-dark);
}

@layer components {
  /* Pornhub-style buttons */
  .btn-ph-primary {
    @apply bg-ph-orange-500 hover:bg-ph-orange-600 text-white font-semibold py-2.5 px-6 rounded-md transition-all duration-200 transform hover:scale-105 active:scale-95;
  }

  .btn-ph-secondary {
    @apply bg-ph-gray-700 hover:bg-ph-gray-600 text-white font-semibold py-2.5 px-6 rounded-md transition-all duration-200;
  }

  .btn-ph-outline {
    @apply border-2 border-ph-orange-500 text-ph-orange-500 hover:bg-ph-orange-500 hover:text-white font-semibold py-2 px-6 rounded-md transition-all duration-200;
  }

  /* Pornhub-style cards */
  .card-ph {
    @apply bg-ph-gray-800 rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-ph-gray-700;
  }

  .card-ph-hover {
    @apply transform hover:scale-105 transition-transform duration-200;
  }

  /* Pornhub-style inputs */
  .input-ph {
    @apply bg-ph-gray-800 text-white rounded-md px-4 py-3 w-full border border-ph-gray-600 focus:outline-none focus:ring-2 focus:ring-ph-orange-500 focus:border-transparent transition-all duration-200;
  }

  .input-ph::placeholder {
    @apply text-ph-gray-400;
  }

  /* Pornhub-style text styles */
  .text-ph-primary {
    @apply text-ph-orange-500;
  }

  .text-ph-muted {
    @apply text-ph-gray-400;
  }

  .text-ph-light {
    @apply text-ph-gray-300;
  }

  /* Video player styles */
  .video-player-container {
    @apply relative w-full bg-black rounded-lg overflow-hidden;
    aspect-ratio: 16/9;
  }

  .video-thumbnail {
    @apply absolute inset-0 w-full h-full object-cover;
  }

  .video-overlay {
    @apply absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity duration-200;
  }

  .play-button {
    @apply w-16 h-16 bg-ph-orange-500 rounded-full flex items-center justify-center text-white text-2xl hover:bg-ph-orange-600 transition-colors duration-200;
  }

  /* Mobile-first responsive utilities */
  .container-ph {
    @apply w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  .grid-ph-videos {
    @apply grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 md:gap-6;
  }

  .grid-ph-categories {
    @apply grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4;
  }
}
