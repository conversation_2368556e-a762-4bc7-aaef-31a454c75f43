import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import User from '@/app/models/User';
import Video from '@/app/models/Video';
import mongoose from 'mongoose';

// Define an interface for the engagement data
interface EngagementData {
  videoId: string;
  action: 'play' | 'pause' | 'seek' | 'complete' | 'timeSpent';
  value?: number; // For timeSpent (in seconds) or seek position
  timestamp?: number; // Client-side timestamp
}

// Create a schema for video engagement if it doesn't exist
let Engagement: mongoose.Model<any>;

try {
  // Try to get the model if it already exists
  Engagement = mongoose.model('Engagement');
} catch (e) {
  // Define the schema if the model doesn't exist
  const EngagementSchema = new mongoose.Schema({
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false, // Allow anonymous tracking
    },
    video: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Video',
      required: true,
    },
    sessionId: {
      type: String,
      required: true,
    },
    action: {
      type: String,
      enum: ['play', 'pause', 'seek', 'complete', 'timeSpent'],
      required: true,
    },
    value: {
      type: Number,
      required: false,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    clientTimestamp: {
      type: Number,
      required: false,
    },
    userAgent: {
      type: String,
      required: false,
    },
    ipAddress: {
      type: String,
      required: false,
    },
  });

  // Create the model
  Engagement = mongoose.model('Engagement', EngagementSchema);
}

export async function POST(req: NextRequest) {
  try {
    // Connect to the database
    await dbConnect();
    
    // Get the request data
    const data: EngagementData = await req.json();
    
    if (!data.videoId || !data.action) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // Get user session
    const session = await getServerSession(authOptions);
    const userId = session?.user ? (session.user as any).id : null;
    
    // Generate a session ID if user is not logged in
    // This uses the request headers to create a pseudo-unique ID
    const userAgent = req.headers.get('user-agent') || '';
    const forwardedFor = req.headers.get('x-forwarded-for') || '';
    const ipAddress = forwardedFor.split(',')[0].trim();
    
    // Create a session ID (either user ID or a hash of user agent + IP)
    const sessionId = userId || 
      Buffer.from(`${userAgent}:${ipAddress}:${data.videoId}`).toString('base64');
    
    // Check if video exists
    const video = await Video.findById(data.videoId);
    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }
    
    // Create engagement record
    const engagement = new Engagement({
      user: userId,
      video: data.videoId,
      sessionId,
      action: data.action,
      value: data.value,
      clientTimestamp: data.timestamp,
      userAgent,
      ipAddress,
    });
    
    await engagement.save();
    
    // If this is a 'complete' action and user is logged in, update user preferences
    if (data.action === 'complete' && userId) {
      // Find user
      const user = await User.findById(userId);
      
      if (user) {
        // Update user's watch history if not already in it
        if (!user.watchHistory.some(id => id.toString() === data.videoId)) {
          user.watchHistory.unshift(data.videoId);
          
          // Limit history to 100 items
          if (user.watchHistory.length > 100) {
            user.watchHistory = user.watchHistory.slice(0, 100);
          }
          
          await user.save();
        }
      }
    }
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error tracking engagement:', error);
    return NextResponse.json(
      { error: 'Failed to track engagement' },
      { status: 500 }
    );
  }
}
