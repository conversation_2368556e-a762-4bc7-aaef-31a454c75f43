import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import mongoose from 'mongoose';
import Video from '@/app/models/Video';
import Category from '@/app/models/Category';

// Create a schema for recommendation tracking if it doesn't exist
let RecommendationTrack: mongoose.Model<any>;

try {
  // Try to get the model if it already exists
  RecommendationTrack = mongoose.model('RecommendationTrack');
} catch (e) {
  // Define the schema if the model doesn't exist
  const RecommendationTrackSchema = new mongoose.Schema({
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false, // Allow anonymous tracking
    },
    video: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Video',
      required: true,
    },
    recommendedVideos: [{
      video: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Video',
      },
      position: Number,
      clicked: {
        type: Boolean,
        default: false
      },
      clickedAt: Date
    }],
    sessionId: {
      type: String,
      required: true,
    },
    timestamp: {
      type: Date,
      default: Date.now,
    },
    recommendationType: {
      type: String,
      enum: ['personalized', 'trending', 'related'],
      required: true,
    }
  });

  // Create the model
  RecommendationTrack = mongoose.model('RecommendationTrack', RecommendationTrackSchema);
}

export async function GET(req: NextRequest) {
  try {
    // Check authentication and admin status
    const session = await getServerSession(authOptions);
    if (!session || !session.user || !(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Connect to the database
    await dbConnect();
    
    // Get time range from query params
    const { searchParams } = new URL(req.url);
    const timeRange = searchParams.get('timeRange') || '7d';
    
    // Calculate date range
    let startDate = new Date();
    switch (timeRange) {
      case '24h':
        startDate.setHours(startDate.getHours() - 24);
        break;
      case '7d':
        startDate.setDate(startDate.getDate() - 7);
        break;
      case '30d':
        startDate.setDate(startDate.getDate() - 30);
        break;
      case 'all':
        startDate = new Date(0); // Beginning of time
        break;
      default:
        startDate.setDate(startDate.getDate() - 7);
    }
    
    // Get total recommendations
    const totalRecommendations = await RecommendationTrack.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate }
        }
      },
      {
        $project: {
          recommendedCount: { $size: '$recommendedVideos' }
        }
      },
      {
        $group: {
          _id: null,
          total: { $sum: '$recommendedCount' }
        }
      }
    ]);
    
    const totalRecommendationsCount = totalRecommendations.length > 0 ? totalRecommendations[0].total : 0;
    
    // Get clicked recommendations
    const clickedRecommendations = await RecommendationTrack.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate }
        }
      },
      {
        $unwind: '$recommendedVideos'
      },
      {
        $match: {
          'recommendedVideos.clicked': true
        }
      },
      {
        $count: 'total'
      }
    ]);
    
    const clickedRecommendationsCount = clickedRecommendations.length > 0 ? clickedRecommendations[0].total : 0;
    
    // Calculate click-through rate
    const clickThroughRate = totalRecommendationsCount > 0 ? clickedRecommendationsCount / totalRecommendationsCount : 0;
    
    // Get top recommended videos
    const topRecommendedVideos = await RecommendationTrack.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate }
        }
      },
      {
        $unwind: '$recommendedVideos'
      },
      {
        $group: {
          _id: '$recommendedVideos.video',
          count: { $sum: 1 },
          clicks: {
            $sum: {
              $cond: [{ $eq: ['$recommendedVideos.clicked', true] }, 1, 0]
            }
          }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      },
      {
        $lookup: {
          from: 'videos',
          localField: '_id',
          foreignField: '_id',
          as: 'videoDetails'
        }
      },
      {
        $unwind: '$videoDetails'
      },
      {
        $project: {
          _id: '$_id',
          title: '$videoDetails.title',
          count: 1,
          clicks: 1,
          ctr: {
            $cond: [
              { $gt: ['$count', 0] },
              { $divide: ['$clicks', '$count'] },
              0
            ]
          }
        }
      }
    ]);
    
    // Get top categories in recommendations
    const topCategories = await RecommendationTrack.aggregate([
      {
        $match: {
          timestamp: { $gte: startDate }
        }
      },
      {
        $unwind: '$recommendedVideos'
      },
      {
        $lookup: {
          from: 'videos',
          localField: 'recommendedVideos.video',
          foreignField: '_id',
          as: 'videoDetails'
        }
      },
      {
        $unwind: '$videoDetails'
      },
      {
        $unwind: '$videoDetails.categories'
      },
      {
        $group: {
          _id: '$videoDetails.categories',
          count: { $sum: 1 }
        }
      },
      {
        $sort: { count: -1 }
      },
      {
        $limit: 10
      },
      {
        $lookup: {
          from: 'categories',
          localField: '_id',
          foreignField: '_id',
          as: 'categoryDetails'
        }
      },
      {
        $unwind: '$categoryDetails'
      },
      {
        $project: {
          _id: '$_id',
          name: '$categoryDetails.name',
          count: 1
        }
      }
    ]);
    
    return NextResponse.json({
      totalRecommendations: totalRecommendationsCount,
      clickedRecommendations: clickedRecommendationsCount,
      clickThroughRate,
      topRecommendedVideos,
      topCategories
    });
  } catch (error) {
    console.error('Error fetching recommendation metrics:', error);
    return NextResponse.json(
      { error: 'Failed to fetch recommendation metrics' },
      { status: 500 }
    );
  }
}
