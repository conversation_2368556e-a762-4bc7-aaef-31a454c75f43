'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>aChart<PERSON><PERSON>, FaUsers, FaVideo, Fa<PERSON>ye, FaThumbsUp } from 'react-icons/fa';
import axios from 'axios';

interface EngagementMetrics {
  totalEngagements: number;
  playCount: number;
  pauseCount: number;
  seekCount: number;
  completeCount: number;
  timeSpentTotal: number;
  averageWatchTime: number;
  completionRate: number;
}

interface RecommendationMetrics {
  totalRecommendations: number;
  clickedRecommendations: number;
  clickThroughRate: number;
  topRecommendedVideos: {
    _id: string;
    title: string;
    count: number;
    clicks: number;
    ctr: number;
  }[];
  topCategories: {
    _id: string;
    name: string;
    count: number;
  }[];
}

export default function RecommendationsDashboard() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeRange, setTimeRange] = useState('7d'); // 24h, 7d, 30d, all
  const [engagementMetrics, setEngagementMetrics] = useState<EngagementMetrics | null>(null);
  const [recommendationMetrics, setRecommendationMetrics] = useState<RecommendationMetrics | null>(null);

  // Check if user is admin
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login?redirect=/admin/recommendations');
    } else if (status === 'authenticated') {
      if (!(session.user as any).isAdmin) {
        router.push('/');
      }
    }
  }, [status, session, router]);

  // Fetch metrics
  useEffect(() => {
    const fetchMetrics = async () => {
      if (status !== 'authenticated' || !(session.user as any).isAdmin) {
        return;
      }

      try {
        setLoading(true);
        
        // Fetch engagement metrics
        const engagementResponse = await axios.get(`/api/admin/analytics/engagement?timeRange=${timeRange}`);
        if (engagementResponse.data) {
          setEngagementMetrics(engagementResponse.data);
        }
        
        // Fetch recommendation metrics
        const recommendationResponse = await axios.get(`/api/admin/analytics/recommendations?timeRange=${timeRange}`);
        if (recommendationResponse.data) {
          setRecommendationMetrics(recommendationResponse.data);
        }
      } catch (error) {
        console.error('Error fetching metrics:', error);
        setError('Failed to load metrics');
      } finally {
        setLoading(false);
      }
    };
    
    fetchMetrics();
  }, [timeRange, status, session]);

  if (status === 'loading' || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <FaSpinner className="animate-spin text-red-500 text-4xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/50 text-white p-4 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Recommendation Analytics</h1>
        
        <div>
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-800 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="all">All Time</option>
          </select>
        </div>
      </div>
      
      {/* Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <FaUsers className="text-red-500 mr-2" />
            <h3 className="text-lg font-semibold text-white">User Engagement</h3>
          </div>
          <p className="text-3xl font-bold text-white">{engagementMetrics?.totalEngagements || 0}</p>
          <p className="text-gray-400">Total interactions</p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <FaVideo className="text-red-500 mr-2" />
            <h3 className="text-lg font-semibold text-white">Video Plays</h3>
          </div>
          <p className="text-3xl font-bold text-white">{engagementMetrics?.playCount || 0}</p>
          <p className="text-gray-400">Total video plays</p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <FaEye className="text-red-500 mr-2" />
            <h3 className="text-lg font-semibold text-white">Completion Rate</h3>
          </div>
          <p className="text-3xl font-bold text-white">{engagementMetrics?.completionRate ? `${(engagementMetrics.completionRate * 100).toFixed(1)}%` : '0%'}</p>
          <p className="text-gray-400">Videos watched to completion</p>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <FaThumbsUp className="text-red-500 mr-2" />
            <h3 className="text-lg font-semibold text-white">Recommendation CTR</h3>
          </div>
          <p className="text-3xl font-bold text-white">{recommendationMetrics?.clickThroughRate ? `${(recommendationMetrics.clickThroughRate * 100).toFixed(1)}%` : '0%'}</p>
          <p className="text-gray-400">Recommendation click-through rate</p>
        </div>
      </div>
      
      {/* Top Recommended Videos */}
      <div className="bg-gray-800 rounded-lg p-6 mb-8">
        <h2 className="text-xl font-bold text-white mb-4">Top Recommended Videos</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="pb-3 text-gray-400">Video</th>
                <th className="pb-3 text-gray-400">Recommendations</th>
                <th className="pb-3 text-gray-400">Clicks</th>
                <th className="pb-3 text-gray-400">CTR</th>
              </tr>
            </thead>
            <tbody>
              {recommendationMetrics?.topRecommendedVideos?.map((video) => (
                <tr key={video._id} className="border-b border-gray-700">
                  <td className="py-3 text-white">{video.title}</td>
                  <td className="py-3 text-white">{video.count}</td>
                  <td className="py-3 text-white">{video.clicks}</td>
                  <td className="py-3 text-white">{(video.ctr * 100).toFixed(1)}%</td>
                </tr>
              )) || (
                <tr>
                  <td colSpan={4} className="py-3 text-center text-gray-400">No data available</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
      
      {/* Top Categories in Recommendations */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-bold text-white mb-4">Top Categories in Recommendations</h2>
        
        <div className="overflow-x-auto">
          <table className="w-full text-left">
            <thead>
              <tr className="border-b border-gray-700">
                <th className="pb-3 text-gray-400">Category</th>
                <th className="pb-3 text-gray-400">Recommendations</th>
              </tr>
            </thead>
            <tbody>
              {recommendationMetrics?.topCategories?.map((category) => (
                <tr key={category._id} className="border-b border-gray-700">
                  <td className="py-3 text-white">{category.name}</td>
                  <td className="py-3 text-white">{category.count}</td>
                </tr>
              )) || (
                <tr>
                  <td colSpan={2} className="py-3 text-center text-gray-400">No data available</td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
