import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';
import User from '@/app/models/User';
import Category from '@/app/models/Category';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !(session.user as any).isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Get current date and calculate date ranges
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const lastMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    // Get total counts
    const [totalVideos, totalUsers, totalCategories] = await Promise.all([
      Video.countDocuments({ isActive: true }),
      User.countDocuments(),
      Category.countDocuments()
    ]);

    // Get videos added today
    const videosToday = await Video.countDocuments({
      isActive: true,
      createdAt: { $gte: today }
    });

    // Get users registered today
    const usersToday = await User.countDocuments({
      createdAt: { $gte: today }
    });

    // Get total views and likes
    const videoStats = await Video.aggregate([
      { $match: { isActive: true } },
      {
        $group: {
          _id: null,
          totalViews: { $sum: '$views' },
          totalLikes: { $sum: '$likes' },
          totalDislikes: { $sum: '$dislikes' }
        }
      }
    ]);

    const { totalViews = 0, totalLikes = 0, totalDislikes = 0 } = videoStats[0] || {};

    // Get views today (this would require a views tracking collection in a real app)
    // For now, we'll estimate based on recent video activity
    const viewsToday = Math.floor(totalViews * 0.05); // Estimate 5% of total views happened today

    // Get top categories by video count
    const topCategories = await Category.aggregate([
      {
        $lookup: {
          from: 'videos',
          localField: '_id',
          foreignField: 'categories',
          as: 'videos'
        }
      },
      {
        $project: {
          name: 1,
          videoCount: { $size: '$videos' }
        }
      },
      { $sort: { videoCount: -1 } },
      { $limit: 5 }
    ]);

    // Get most viewed videos
    const topVideos = await Video.find({ isActive: true })
      .sort({ views: -1 })
      .limit(5)
      .select('title views likes slug')
      .lean();

    // Get recent activity (last 7 days)
    const recentVideos = await Video.find({
      isActive: true,
      createdAt: { $gte: lastWeek }
    })
      .sort({ createdAt: -1 })
      .limit(10)
      .select('title createdAt views')
      .lean();

    // Generate traffic by location (mock data for now)
    const trafficByLocation = [
      { country: 'United States', visitors: Math.floor(totalViews * 0.35), percentage: 35 },
      { country: 'United Kingdom', visitors: Math.floor(totalViews * 0.15), percentage: 15 },
      { country: 'Germany', visitors: Math.floor(totalViews * 0.12), percentage: 12 },
      { country: 'France', visitors: Math.floor(totalViews * 0.10), percentage: 10 },
      { country: 'Canada', visitors: Math.floor(totalViews * 0.08), percentage: 8 },
      { country: 'Australia', visitors: Math.floor(totalViews * 0.06), percentage: 6 },
      { country: 'Japan', visitors: Math.floor(totalViews * 0.05), percentage: 5 },
      { country: 'Others', visitors: Math.floor(totalViews * 0.09), percentage: 9 }
    ];

    // Generate daily stats for the last 7 days
    const dailyStats = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
      const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
      const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

      const [dayVideos, dayUsers] = await Promise.all([
        Video.countDocuments({
          isActive: true,
          createdAt: { $gte: dayStart, $lt: dayEnd }
        }),
        User.countDocuments({
          createdAt: { $gte: dayStart, $lt: dayEnd }
        })
      ]);

      dailyStats.push({
        date: date.toISOString().split('T')[0],
        videos: dayVideos,
        users: dayUsers,
        views: Math.floor(Math.random() * 1000) + 500 // Mock views data
      });
    }

    // Calculate growth percentages
    const videosYesterday = await Video.countDocuments({
      isActive: true,
      createdAt: { $gte: yesterday, $lt: today }
    });

    const usersYesterday = await User.countDocuments({
      createdAt: { $gte: yesterday, $lt: today }
    });

    const videoGrowth = videosYesterday > 0 ? 
      ((videosToday - videosYesterday) / videosYesterday * 100) : 
      (videosToday > 0 ? 100 : 0);

    const userGrowth = usersYesterday > 0 ? 
      ((usersToday - usersYesterday) / usersYesterday * 100) : 
      (usersToday > 0 ? 100 : 0);

    const viewGrowth = Math.floor(Math.random() * 20) - 10; // Mock growth between -10% and +10%

    return NextResponse.json({
      overview: {
        totalVideos,
        totalUsers,
        totalViews,
        totalCategories,
        videosToday,
        usersToday,
        viewsToday,
        totalLikes,
        totalDislikes,
        videoGrowth: Math.round(videoGrowth * 100) / 100,
        userGrowth: Math.round(userGrowth * 100) / 100,
        viewGrowth: Math.round(viewGrowth * 100) / 100
      },
      topCategories,
      topVideos,
      recentActivity: recentVideos,
      trafficByLocation,
      dailyStats
    });

  } catch (error: any) {
    console.error('Error fetching analytics:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch analytics' },
      { status: 500 }
    );
  }
}
