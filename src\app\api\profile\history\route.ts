import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import User from '@/app/models/User';
import Video from '@/app/models/Video';

export async function GET(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to access your watch history' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find user
    const user = await User.findOne({ email: session.user.email });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Get video IDs from watch history
    const videoIds = user.watchHistory.map((item) => item.video);

    // Find videos
    const videos = await Video.find({
      _id: { $in: videoIds },
      isActive: true
    })
      .select('title thumbnail duration views likes slug');

    // Sort videos by watch date
    const sortedVideos = videos.sort((a, b) => {
      const aIndex = user.watchHistory.findIndex(
        (item) => item.video.toString() === a._id.toString()
      );
      const bIndex = user.watchHistory.findIndex(
        (item) => item.video.toString() === b._id.toString()
      );

      const aDate = user.watchHistory[aIndex].watchedAt;
      const bDate = user.watchHistory[bIndex].watchedAt;

      return new Date(bDate).getTime() - new Date(aDate).getTime();
    });

    return NextResponse.json({ videos: sortedVideos });
  } catch (error) {
    console.error('Error fetching watch history:', error);
    return NextResponse.json(
      { error: 'Failed to fetch watch history' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const { videoId } = await req.json();

    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }

    // Get user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to update watch history' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find user
    const user = await User.findOne({ email: session.user.email });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if video exists
    const video = await Video.findById(videoId);

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Check if video is already in watch history
    const existingIndex = user.watchHistory.findIndex(
      (item) => item.toString() === videoId
    );

    if (existingIndex !== -1) {
      // Remove the existing entry so we can add it to the front
      user.watchHistory.splice(existingIndex, 1);
    }

    // Add to the beginning of watch history (most recent first)
    user.watchHistory.unshift(videoId);

    // Limit watch history to 100 items
    if (user.watchHistory.length > 100) {
      user.watchHistory = user.watchHistory.slice(0, 100);
    }

    await user.save();

    return NextResponse.json({ message: 'Watch history updated' });
  } catch (error) {
    console.error('Error updating watch history:', error);
    return NextResponse.json(
      { error: 'Failed to update watch history' },
      { status: 500 }
    );
  }
}

export async function DELETE(req: NextRequest) {
  try {
    // Get user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to clear watch history' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find user
    const user = await User.findOne({ email: session.user.email });

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Clear watch history
    user.watchHistory = [];
    await user.save();

    return NextResponse.json({ message: 'Watch history cleared' });
  } catch (error) {
    console.error('Error clearing watch history:', error);
    return NextResponse.json(
      { error: 'Failed to clear watch history' },
      { status: 500 }
    );
  }
}
