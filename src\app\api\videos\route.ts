import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';
import Category from '@/app/models/Category';

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const category = searchParams.get('category');
    const search = searchParams.get('search');
    const sort = searchParams.get('sort') || 'newest';

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Connect to the database
    await dbConnect();

    // Build query
    let query: any = { isActive: true };

    // Add category filter if provided
    if (category) {
      // Find category ID first
      const categoryObj = await Category.findOne({ slug: category });

      if (categoryObj) {
        query.categories = { $in: [categoryObj._id] };
      }
    }

    // Add search filter if provided
    if (search) {
      query.$or = [
        { title: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    // Build sort options
    let sortOptions: any = {};
    switch (sort) {
      case 'newest':
        sortOptions = { createdAt: -1 };
        break;
      case 'oldest':
        sortOptions = { createdAt: 1 };
        break;
      case 'most-viewed':
        sortOptions = { views: -1 };
        break;
      case 'most-liked':
        sortOptions = { likes: -1 };
        break;
      case 'duration-asc':
        sortOptions = { duration: 1 };
        break;
      case 'duration-desc':
        sortOptions = { duration: -1 };
        break;
      default:
        sortOptions = { createdAt: -1 };
    }

    // Execute query with pagination
    const videos = await Video.find(query)
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .populate('categories', 'name slug');

    // Get total count for pagination
    const total = await Video.countDocuments(query);

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      videos,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching videos:', error);
    return NextResponse.json(
      { error: 'Failed to fetch videos' },
      { status: 500 }
    );
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    // Connect to the database
    await dbConnect();

    // Create new video
    const video = await Video.create(body);

    return NextResponse.json(video, { status: 201 });
  } catch (error) {
    console.error('Error creating video:', error);
    return NextResponse.json(
      { error: 'Failed to create video' },
      { status: 500 }
    );
  }
}
