'use client';

import { useState } from 'react';
import { FaPlus, FaSearch, FaEdit, FaTrash, <PERSON>a<PERSON><PERSON>, FaThumbsUp } from 'react-icons/fa';
import Link from 'next/link';
import Image from 'next/image';

// Mock data for demonstration
const videos = [
  {
    id: '1',
    title: 'Hot blonde in action with her boyfriend',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 845, // 14:05
    views: 1250000,
    likes: 24500,
    categories: ['Blonde', 'Amateur', 'Couple'],
    isActive: true,
    createdAt: '2023-05-10'
  },
  {
    id: '2',
    title: '<PERSON>runette gets wild in the bedroom',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1230, // 20:30
    views: 980000,
    likes: 18700,
    categories: ['Brunette', 'Amateur'],
    isActive: true,
    createdAt: '2023-05-09'
  },
  {
    id: '3',
    title: 'Steamy shower scene with two girls',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 720, // 12:00
    views: 1500000,
    likes: 32000,
    categories: ['Lesbian', 'Shower'],
    isActive: true,
    createdAt: '2023-05-08'
  },
  {
    id: '4',
    title: 'Office romance turns into something more',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 950, // 15:50
    views: 750000,
    likes: 15600,
    categories: ['Office', 'Roleplay'],
    isActive: false,
    createdAt: '2023-05-07'
  },
  {
    id: '5',
    title: 'Passionate couple on vacation',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1120, // 18:40
    views: 2100000,
    likes: 45000,
    categories: ['Couple', 'Vacation'],
    isActive: true,
    createdAt: '2023-05-06'
  },
];

// Format duration from seconds to MM:SS
const formatDuration = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const remainingSeconds = seconds % 60;
  return `${minutes}:${remainingSeconds < 10 ? '0' : ''}${remainingSeconds}`;
};

// Format views count (e.g., 1.2K, 3.5M)
const formatCount = (count: number) => {
  if (count >= 1000000) {
    return `${(count / 1000000).toFixed(1)}M`;
  } else if (count >= 1000) {
    return `${(count / 1000).toFixed(1)}K`;
  }
  return count.toString();
};

export default function AdminVideos() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [statusFilter, setStatusFilter] = useState('all');
  
  // Get unique categories from all videos
  const categories = ['all', ...new Set(videos.flatMap(video => video.categories))].sort();
  
  // Filter videos based on search query, selected category, and status
  const filteredVideos = videos.filter(video => {
    const matchesQuery = video.title.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || video.categories.includes(selectedCategory);
    const matchesStatus = statusFilter === 'all' || 
                          (statusFilter === 'active' && video.isActive) || 
                          (statusFilter === 'inactive' && !video.isActive);
    return matchesQuery && matchesCategory && matchesStatus;
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Manage Videos</h1>
        <Link href="/admin/videos/add" className="btn-primary flex items-center">
          <FaPlus className="mr-2" /> Add New Video
        </Link>
      </div>
      
      {/* Filters */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <form onSubmit={handleSearch}>
            <div className="flex">
              <input
                type="text"
                placeholder="Search videos..."
                className="input-field flex-grow"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <button type="submit" className="ml-2 p-2 bg-orange-500 rounded hover:bg-orange-600">
                <FaSearch />
              </button>
            </div>
          </form>
          
          <div>
            <select 
              className="input-field w-full"
              value={selectedCategory}
              onChange={(e) => setSelectedCategory(e.target.value)}
            >
              <option value="all">All Categories</option>
              {categories.filter(cat => cat !== 'all').map(category => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>
          </div>
          
          <div>
            <select 
              className="input-field w-full"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
            >
              <option value="all">All Status</option>
              <option value="active">Active</option>
              <option value="inactive">Inactive</option>
            </select>
          </div>
        </div>
      </div>
      
      {/* Videos Table */}
      <div className="bg-gray-800 rounded-lg shadow-md overflow-hidden">
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-700">
                <th className="px-4 py-3 text-left text-white">Video</th>
                <th className="px-4 py-3 text-left text-white">Categories</th>
                <th className="px-4 py-3 text-left text-white">Stats</th>
                <th className="px-4 py-3 text-left text-white">Status</th>
                <th className="px-4 py-3 text-left text-white">Date</th>
                <th className="px-4 py-3 text-left text-white">Actions</th>
              </tr>
            </thead>
            <tbody>
              {filteredVideos.map((video) => (
                <tr key={video.id} className="border-t border-gray-700">
                  <td className="px-4 py-3">
                    <div className="flex items-center">
                      <div className="relative w-20 h-12 mr-3">
                        <Image
                          src={video.thumbnail}
                          alt={video.title}
                          fill
                          className="object-cover rounded"
                        />
                        <div className="absolute bottom-0 right-0 bg-black bg-opacity-70 text-white text-xs px-1 rounded">
                          {formatDuration(video.duration)}
                        </div>
                      </div>
                      <span className="text-white">{video.title}</span>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex flex-wrap gap-1">
                      {video.categories.map((category) => (
                        <span key={category} className="bg-gray-700 text-xs text-gray-300 px-2 py-1 rounded">
                          {category}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <div className="text-gray-400 text-sm">
                      <div className="flex items-center mb-1">
                        <FaEye className="mr-1" /> {formatCount(video.views)} views
                      </div>
                      <div className="flex items-center">
                        <FaThumbsUp className="mr-1" /> {formatCount(video.likes)} likes
                      </div>
                    </div>
                  </td>
                  <td className="px-4 py-3">
                    <span className={`px-2 py-1 rounded text-xs ${video.isActive ? 'bg-green-900 text-green-300' : 'bg-red-900 text-red-300'}`}>
                      {video.isActive ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-4 py-3 text-gray-400">
                    {video.createdAt}
                  </td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <Link href={`/admin/videos/edit/${video.id}`} className="p-2 bg-blue-600 text-white rounded hover:bg-blue-700">
                        <FaEdit />
                      </Link>
                      <button className="p-2 bg-red-600 text-white rounded hover:bg-red-700">
                        <FaTrash />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
