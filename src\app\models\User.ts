import mongoose, { Schema, Document } from 'mongoose';
import bcrypt from 'bcrypt';

export interface I<PERSON>ser extends Document {
  username: string;
  email: string;
  password: string;
  isAdmin: boolean;
  isVerified: boolean;
  dateOfBirth: Date;
  favorites: mongoose.Types.ObjectId[];
  watchHistory: mongoose.Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
  comparePassword(candidatePassword: string): Promise<boolean>;
}

const UserSchema: Schema = new Schema(
  {
    username: {
      type: String,
      required: true,
      unique: true,
      trim: true,
      minlength: [3, 'Username must be at least 3 characters long'],
    },
    email: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    password: {
      type: String,
      required: true,
      minlength: [8, 'Password must be at least 8 characters long'],
    },
    isAdmin: { type: Boolean, default: false },
    isVerified: { type: Boolean, default: false },
    dateOfBirth: {
      type: Date,
      required: [true, 'Date of birth is required for age verification'],
    },
    favorites: [{
      type: Schema.Types.ObjectId,
      ref: 'Video'
    }],
    watchHistory: [{
      type: Schema.Types.ObjectId,
      ref: 'Video'
    }],
  },
  { timestamps: true }
);

// Hash password before saving
UserSchema.pre('save', async function(next) {
  const user = this as IUser;

  // Only hash the password if it has been modified (or is new)
  if (!this.isModified('password')) return next();

  try {
    // Generate salt
    const salt = await bcrypt.genSalt(10);

    // Hash the password along with the new salt
    const hashedPassword = await bcrypt.hash(user.password, salt);

    // Replace the plaintext password with the hashed one
    user.password = hashedPassword;
    next();
  } catch (error: any) {
    next(error);
  }
});

// Method to compare passwords
UserSchema.methods.comparePassword = async function(candidatePassword: string): Promise<boolean> {
  try {
    return await bcrypt.compare(candidatePassword, this.password);
  } catch (error) {
    return false;
  }
};

export default mongoose.models.User || mongoose.model<IUser>('User', UserSchema);
