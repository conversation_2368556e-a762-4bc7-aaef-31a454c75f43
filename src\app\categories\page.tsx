'use client';

import { useState, useEffect } from 'react';
import CategoryCard from '../components/CategoryCard';
import { FaSpinner } from 'react-icons/fa';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
  description: string;
  thumbnailUrl: string;
  isActive: boolean;
  videoCount?: number;
}

export default function CategoriesPage() {
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true);

        // Fetch categories with video counts
        const response = await axios.get('/api/categories?withCounts=true');

        if (response.data && response.data.categories) {
          setCategories(response.data.categories);
        } else {
          setError('Failed to load categories');
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        setError('An error occurred while fetching categories');
      } finally {
        setLoading(false);
      }
    };

    fetchCategories();
  }, []);

  return (
    <div>
      <h1 className="text-3xl font-bold text-white mb-8">All Categories</h1>

      {loading && (
        <div className="flex justify-center items-center py-12">
          <FaSpinner className="animate-spin text-red-500 text-4xl" />
        </div>
      )}

      {error && !loading && (
        <div className="bg-red-900/50 text-white p-4 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}

      {!loading && !error && categories.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {categories.map((category) => (
            <CategoryCard
              key={category._id}
              id={category._id}
              name={category.name}
              slug={category.slug}
              thumbnail={category.thumbnailUrl || `https://via.placeholder.com/640x360.png?text=${encodeURIComponent(category.name)}`}
              videoCount={category.videoCount || 0}
              description={category.description}
            />
          ))}
        </div>
      )}

      {!loading && !error && categories.length === 0 && (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <h3 className="text-xl font-semibold text-white mb-2">No categories found</h3>
          <p className="text-gray-400">Check back later for new categories.</p>
        </div>
      )}
    </div>
  );
}
