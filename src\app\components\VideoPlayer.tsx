'use client';

import { useState, useEffect, useRef } from 'react';
import ReactPlayer from 'react-player';
import { FaPlay, FaPause, FaExpand, FaCompress, FaVolumeUp, FaVolumeMute } from 'react-icons/fa';
import { useSession } from 'next-auth/react';
import axios from 'axios';

interface VideoPlayerProps {
  videoUrl: string;
  videoId: string;
  title: string;
  poster?: string;
  isEmbedded?: boolean;
}

const VideoPlayer: React.FC<VideoPlayerProps> = ({ videoUrl, videoId, title, poster, isEmbedded = false }) => {
  const [playing, setPlaying] = useState(false);
  const [volume, setVolume] = useState(0.8);
  const [muted, setMuted] = useState(false);
  const [played, setPlayed] = useState(0);
  const [loaded, setLoaded] = useState(0);
  const [duration, setDuration] = useState(0);
  const [fullscreen, setFullscreen] = useState(false);
  const [seeking, setSeeking] = useState(false);
  const [watchedEnough, setWatchedEnough] = useState(false);
  const [lastTimeUpdate, setLastTimeUpdate] = useState(0);
  const [playedSeconds, setPlayedSeconds] = useState(0);

  const playerRef = useRef<ReactPlayer>(null);
  const playerContainerRef = useRef<HTMLDivElement>(null);
  const progressBarRef = useRef<HTMLDivElement>(null);

  const { data: session } = useSession();

  // Track engagement events
  const trackEngagement = async (action: 'play' | 'pause' | 'seek' | 'complete' | 'timeSpent', value?: number) => {
    try {
      await axios.post('/api/analytics/engagement', {
        videoId,
        action,
        value,
        timestamp: Date.now()
      });
    } catch (error) {
      console.error('Error tracking engagement:', error);
      // Non-critical error, don't show to user
    }
  };

  // Handle fullscreen
  const toggleFullscreen = () => {
    if (!document.fullscreenElement) {
      playerContainerRef.current?.requestFullscreen();
      setFullscreen(true);
    } else {
      document.exitFullscreen();
      setFullscreen(false);
    }
  };

  // Handle fullscreen change event
  useEffect(() => {
    const handleFullscreenChange = () => {
      setFullscreen(!!document.fullscreenElement);
    };

    document.addEventListener('fullscreenchange', handleFullscreenChange);
    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
    };
  }, []);

  // Record view after watching 10% of the video
  useEffect(() => {
    if (played > 0.1 && !watchedEnough) {
      setWatchedEnough(true);

      // Track that user has watched enough of the video
      trackEngagement('timeSpent', Math.floor(duration * 0.1));
    }
  }, [played, watchedEnough, duration]);

  // Track time spent watching
  useEffect(() => {
    // Only track time spent every 10 seconds
    const now = Date.now();
    if (playing && now - lastTimeUpdate > 10000) {
      setLastTimeUpdate(now);
      trackEngagement('timeSpent', Math.floor(playedSeconds));
    }
  }, [playing, playedSeconds, lastTimeUpdate]);

  // Track video completion
  useEffect(() => {
    if (played > 0.9) {
      trackEngagement('complete');
    }
  }, [played]);

  // Format time (seconds to MM:SS)
  const formatTime = (seconds: number) => {
    if (isNaN(seconds)) return '00:00';

    const date = new Date(seconds * 1000);
    const hh = date.getUTCHours();
    const mm = date.getUTCMinutes();
    const ss = date.getUTCSeconds().toString().padStart(2, '0');

    if (hh) {
      return `${hh}:${mm.toString().padStart(2, '0')}:${ss}`;
    }

    return `${mm}:${ss}`;
  };

  // Handle progress bar click
  const handleProgressBarClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!progressBarRef.current) return;

    const rect = progressBarRef.current.getBoundingClientRect();
    const pos = (e.clientX - rect.left) / rect.width;

    playerRef.current?.seekTo(pos);
    setPlayed(pos);

    // Track seek event
    trackEngagement('seek', pos * duration);
  };

  // Handle volume change
  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    setMuted(newVolume === 0);
  };

  // Toggle mute
  const toggleMute = () => {
    setMuted(!muted);
  };

  return (
    <div
      ref={playerContainerRef}
      className="relative bg-black w-full h-full"
    >
      {isEmbedded ? (
        // For embedded videos, use dangerouslySetInnerHTML to render the embed code
        <div
          className="w-full h-full"
          dangerouslySetInnerHTML={{ __html: videoUrl }}
        />
      ) : (
        // For regular videos, use ReactPlayer
        <ReactPlayer
          ref={playerRef}
          url={videoUrl}
          width="100%"
          height="100%"
          playing={playing}
          volume={volume}
          muted={muted}
          onProgress={({ played, loaded, playedSeconds }) => {
            if (!seeking) {
              setPlayed(played);
              setLoaded(loaded);
              setPlayedSeconds(playedSeconds);
            }
          }}
          onDuration={setDuration}
          onPause={() => setPlaying(false)}
          onPlay={() => setPlaying(true)}
          onEnded={() => setPlaying(false)}
          light={poster}
          config={{
            file: {
              attributes: {
                controlsList: 'nodownload',
                disablePictureInPicture: true,
              },
            },
          }}
        />
      )}

      {/* Custom controls - only show for non-embedded videos */}
      {!isEmbedded && (
        <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-2 transition-opacity opacity-0 hover:opacity-100">
          {/* Progress bar */}
          <div
            ref={progressBarRef}
            className="w-full h-2 bg-gray-700 cursor-pointer mb-2"
            onClick={handleProgressBarClick}
          >
            <div
              className="h-full bg-red-600"
              style={{ width: `${played * 100}%` }}
            />
            <div
              className="h-full bg-gray-500 relative top-[-8px]"
              style={{ width: `${loaded * 100}%`, opacity: 0.5 }}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              {/* Play/Pause button */}
              <button
                onClick={() => {
                  const newPlayingState = !playing;
                  setPlaying(newPlayingState);
                  trackEngagement(newPlayingState ? 'play' : 'pause');
                }}
                className="text-white hover:text-red-500 transition-colors"
              >
                {playing ? <FaPause size={20} /> : <FaPlay size={20} />}
              </button>

              {/* Volume control */}
              <div className="flex items-center space-x-2">
                <button
                  onClick={toggleMute}
                  className="text-white hover:text-red-500 transition-colors"
                >
                  {muted || volume === 0 ? <FaVolumeMute size={20} /> : <FaVolumeUp size={20} />}
                </button>
                <input
                  type="range"
                  min={0}
                  max={1}
                  step={0.1}
                  value={volume}
                  onChange={handleVolumeChange}
                  className="w-20 accent-red-500"
                />
              </div>

              {/* Time display */}
              <div className="text-white text-sm">
                {formatTime(played * duration)} / {formatTime(duration)}
              </div>
            </div>

            {/* Fullscreen button */}
            <button
              onClick={toggleFullscreen}
              className="text-white hover:text-red-500 transition-colors"
            >
              {fullscreen ? <FaCompress size={20} /> : <FaExpand size={20} />}
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPlayer;
