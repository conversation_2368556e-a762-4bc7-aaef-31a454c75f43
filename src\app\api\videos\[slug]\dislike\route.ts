import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';
import User from '@/app/models/User';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

export async function POST(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = (session.user as any).id;
    const slug = params.slug;

    // Connect to the database
    await dbConnect();

    // Find video by slug
    const video = await Video.findOne({ slug });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user already disliked this video
    const dislikedIndex = user.dislikedVideos?.findIndex(
      (id: any) => id.toString() === video._id.toString()
    );

    // Check if user already liked this video
    const likedIndex = user.likedVideos?.findIndex(
      (id: any) => id.toString() === video._id.toString()
    );

    // If user already disliked the video, return error
    if (dislikedIndex !== -1 && dislikedIndex !== undefined) {
      return NextResponse.json(
        { error: 'You already disliked this video' },
        { status: 400 }
      );
    }

    // If user liked the video, remove the like
    if (likedIndex !== -1 && likedIndex !== undefined) {
      user.likedVideos.splice(likedIndex, 1);
      video.likes = Math.max(0, video.likes - 1);
    }

    // Add video to user's disliked videos
    if (!user.dislikedVideos) {
      user.dislikedVideos = [];
    }
    user.dislikedVideos.push(video._id);

    // Increment video dislikes
    video.dislikes += 1;

    // Save changes
    await user.save();
    await video.save();

    return NextResponse.json({ 
      success: true, 
      likes: video.likes,
      dislikes: video.dislikes
    });
  } catch (error) {
    console.error('Error disliking video:', error);
    return NextResponse.json(
      { error: 'Failed to dislike video' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const userId = (session.user as any).id;
    const slug = params.slug;

    // Connect to the database
    await dbConnect();

    // Find video by slug
    const video = await Video.findOne({ slug });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Find user
    const user = await User.findById(userId);
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user disliked this video
    const dislikedIndex = user.dislikedVideos?.findIndex(
      (id: any) => id.toString() === video._id.toString()
    );

    // If user didn't dislike the video, return error
    if (dislikedIndex === -1 || dislikedIndex === undefined) {
      return NextResponse.json(
        { error: 'You have not disliked this video' },
        { status: 400 }
      );
    }

    // Remove video from user's disliked videos
    user.dislikedVideos.splice(dislikedIndex, 1);

    // Decrement video dislikes
    video.dislikes = Math.max(0, video.dislikes - 1);

    // Save changes
    await user.save();
    await video.save();

    return NextResponse.json({ 
      success: true, 
      dislikes: video.dislikes 
    });
  } catch (error) {
    console.error('Error removing dislike from video:', error);
    return NextResponse.json(
      { error: 'Failed to remove dislike from video' },
      { status: 500 }
    );
  }
}
