'use client';

import Link from 'next/link';
import VideoThumbnail from './VideoThumbnail';

interface CategoryCardProps {
  id: string;
  name: string;
  slug: string;
  thumbnail: string;
  videoCount: number;
  description?: string;
}

const CategoryCard: React.FC<CategoryCardProps> = ({
  id,
  name,
  slug,
  thumbnail,
  videoCount,
  description,
}) => {
  return (
    <Link href={`/categories/${slug}`}>
      <div className="card group cursor-pointer">
        <div className="relative aspect-video overflow-hidden">
          <img
            src={thumbnail}
            alt={name}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent flex items-end">
            <div className="p-4 w-full">
              <h3 className="text-white font-bold text-lg">{name}</h3>
              <p className="text-gray-300 text-sm">{videoCount} videos</p>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default CategoryCard;
