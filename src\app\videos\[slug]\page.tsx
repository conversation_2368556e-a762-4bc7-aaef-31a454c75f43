'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FaEye, FaThumbsUp, FaThumbsDown, FaShare, FaFlag, FaTag, FaHeart, FaClock } from 'react-icons/fa';
import { useSession } from 'next-auth/react';
import CommentSection from '@/app/components/CommentSection';
import VideoCard from '@/app/components/VideoCard';
import VideoPlayer from '@/app/components/VideoPlayer';
import RecommendedVideos from '@/app/components/RecommendedVideos';
import { formatCount, formatDuration } from '@/app/utils/formatters';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
}

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: Category[];
  tags: string[];
  uploadedBy: string;
  isPublished: boolean;
  isVerified: boolean;
  isEmbedded?: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function VideoPage({ params }: { params: Promise<{ slug: string }> }) {
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [video, setVideo] = useState<Video | null>(null);
  const [relatedVideos, setRelatedVideos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [slug, setSlug] = useState<string>('');

  const { data: session } = useSession();
  const router = useRouter();

  // Unwrap params Promise
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setSlug(resolvedParams.slug);
    };
    unwrapParams();
  }, [params]);

  // Fetch video data
  useEffect(() => {
    const fetchVideoData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/videos/${slug}`);

        if (response.data && response.data.video) {
          setVideo(response.data.video);

          // Increment view count
          await axios.post(`/api/videos/${slug}/view`);

          // Add to watch history if user is logged in
          if (session && session.user) {
            try {
              await axios.post('/api/profile/history', {
                videoId: response.data.video._id
              });
            } catch (historyError) {
              console.error('Error adding to watch history:', historyError);
              // Non-critical error, don't show to user
            }
          }

          // Fetch related videos based on categories
          if (response.data.video.categories && response.data.video.categories.length > 0) {
            const categoryIds = response.data.video.categories.map((cat: Category) => cat._id);
            const relatedResponse = await axios.get(`/api/videos?limit=6&categories=${categoryIds.join(',')}&exclude=${response.data.video._id}`);

            if (relatedResponse.data && relatedResponse.data.videos) {
              setRelatedVideos(relatedResponse.data.videos);
            }
          }
        } else {
          setError('Video not found');
        }
      } catch (error) {
        console.error('Error fetching video:', error);
        setError('Failed to load video');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchVideoData();
    }
  }, [slug, session]);

  // Check if video is in favorites
  useEffect(() => {
    if (session && video) {
      const checkFavoriteStatus = async () => {
        try {
          const response = await axios.get('/api/profile/favorites');

          if (response.data && response.data.favorites) {
            setIsFavorite(response.data.favorites.some((fav: any) => fav._id === video._id));
          }
        } catch (error) {
          console.error('Error checking favorite status:', error);
        }
      };

      checkFavoriteStatus();
    }
  }, [session, video]);

  const handleLike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isLiked) {
        // Unlike the video
        await axios.delete(`/api/videos/${video._id}/like`);
        setIsLiked(false);
        setVideo(prev => prev ? { ...prev, likes: prev.likes - 1 } : null);
      } else {
        // Like the video
        await axios.post(`/api/videos/${video._id}/like`);
        setIsLiked(true);

        // If it was previously disliked, decrement dislikes
        if (isDisliked) {
          setIsDisliked(false);
          setVideo(prev => prev ? {
            ...prev,
            likes: prev.likes + 1,
            dislikes: prev.dislikes - 1
          } : null);
        } else {
          setVideo(prev => prev ? { ...prev, likes: prev.likes + 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error liking video:', error);
    }
  };

  const handleDislike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isDisliked) {
        // Remove dislike
        await axios.delete(`/api/videos/${video._id}/dislike`);
        setIsDisliked(false);
        setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes - 1 } : null);
      } else {
        // Dislike the video
        await axios.post(`/api/videos/${video._id}/dislike`);
        setIsDisliked(true);

        // If it was previously liked, decrement likes
        if (isLiked) {
          setIsLiked(false);
          setVideo(prev => prev ? {
            ...prev,
            dislikes: prev.dislikes + 1,
            likes: prev.likes - 1
          } : null);
        } else {
          setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes + 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error disliking video:', error);
    }
  };

  const toggleFavorite = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isFavorite) {
        // Remove from favorites
        await axios.delete(`/api/profile/favorites?videoId=${video._id}`);
      } else {
        // Add to favorites
        await axios.post('/api/profile/favorites', {
          videoId: video._id
        });
      }

      setIsFavorite(!isFavorite);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-[60vh] flex items-center justify-center">
        <div className="text-gray-400 text-xl">Loading video...</div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="w-full min-h-[60vh] flex flex-col items-center justify-center">
        <div className="text-red-500 text-xl mb-4">{error || 'Video not found'}</div>
        <Link href="/" className="btn-primary">
          Return to Homepage
        </Link>
      </div>
    );
  }

  return (
    <div className="flex flex-col lg:flex-row gap-8">
      {/* Video Player and Info */}
      <div className="lg:w-2/3">
        {/* Video Player */}
        <div className="rounded-lg overflow-hidden mb-4">
          <VideoPlayer
            videoUrl={video.videoUrl}
            videoId={video._id}
            title={video.title}
            poster={video.thumbnailUrl}
            isEmbedded={video.isEmbedded}
          />
        </div>

        {/* Video Title and Actions */}
        <h1 className="text-2xl font-bold text-white mb-4">{video.title}</h1>

        <div className="flex flex-wrap justify-between items-center mb-6">
          <div className="flex items-center text-gray-400 mb-2 md:mb-0 space-x-4">
            <span className="flex items-center">
              <FaEye className="mr-1" /> {formatCount(video.views)} views
            </span>
            <span className="flex items-center">
              <FaClock className="mr-1" /> {formatDuration(video.duration)}
            </span>
          </div>

          <div className="flex space-x-4">
            <button
              className={`flex items-center ${isLiked ? 'text-red-500' : 'text-gray-400'} hover:text-red-500`}
              onClick={handleLike}
            >
              <FaThumbsUp className="mr-1" /> {formatCount(video.likes)}
            </button>

            <button
              className={`flex items-center ${isDisliked ? 'text-red-500' : 'text-gray-400'} hover:text-red-500`}
              onClick={handleDislike}
            >
              <FaThumbsDown className="mr-1" /> {formatCount(video.dislikes)}
            </button>

            <button
              className={`flex items-center ${isFavorite ? 'text-red-500' : 'text-gray-400'} hover:text-red-500`}
              onClick={toggleFavorite}
            >
              <FaHeart className="mr-1" /> {isFavorite ? 'Favorited' : 'Favorite'}
            </button>

            <button className="flex items-center text-gray-400 hover:text-red-500">
              <FaShare className="mr-1" /> Share
            </button>

            <button className="flex items-center text-gray-400 hover:text-red-500">
              <FaFlag className="mr-1" /> Report
            </button>
          </div>
        </div>

        {/* Categories and Tags */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2 mb-3">
            {video.categories && video.categories.map((category) => (
              <Link
                key={category._id}
                href={`/categories/${category.slug}`}
                className="bg-gray-800 hover:bg-gray-700 text-white text-sm px-3 py-1 rounded-full"
              >
                {category.name}
              </Link>
            ))}
          </div>

          <div className="flex flex-wrap gap-2">
            {video.tags && video.tags.map((tag) => (
              <Link
                key={tag}
                href={`/search?q=${tag}`}
                className="flex items-center text-sm text-gray-400 hover:text-red-500"
              >
                <FaTag className="mr-1" /> {tag}
              </Link>
            ))}
          </div>
        </div>

        {/* Video Description */}
        <div className="bg-gray-800 rounded-lg p-4 mb-8">
          <h3 className="text-lg font-semibold text-white mb-2">Description</h3>
          <p className="text-gray-300 whitespace-pre-line">{video.description}</p>
        </div>

        {/* Comments Section */}
        <CommentSection videoSlug={slug} videoId={video._id} />
      </div>

      {/* Sidebar with Recommendations */}
      <div className="lg:w-1/3">
        {/* Related Videos based on categories */}
        <div className="mb-8">
          <h3 className="text-xl font-bold text-white mb-4">Related Videos</h3>
          {relatedVideos.length > 0 ? (
            <div className="grid grid-cols-1 gap-4">
              {relatedVideos.map((video) => (
                <VideoCard
                  key={video._id}
                  id={video._id}
                  title={video.title}
                  thumbnail={video.thumbnailUrl}
                  duration={video.duration}
                  views={video.views}
                  likes={video.likes}
                  slug={video.slug}
                />
              ))}
            </div>
          ) : (
            <div className="text-gray-400">No related videos found</div>
          )}
        </div>

        {/* Personalized Recommendations */}
        <div className="mt-8">
          <RecommendedVideos
            excludeVideoId={video?._id}
            limit={4}
            title="You May Also Like"
          />
        </div>
      </div>
    </div>
  );
}
