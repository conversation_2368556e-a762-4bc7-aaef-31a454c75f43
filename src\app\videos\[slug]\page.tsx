'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FaEye, FaThumbsUp, FaThumbsDown, FaShare, FaFlag, FaTag, FaHeart, FaClock, FaCalendarAlt, FaInfoCircle, FaPlay, FaExclamationTriangle } from 'react-icons/fa';
import { useSession } from 'next-auth/react';
import CommentSection from '@/app/components/CommentSection';
import VideoCard from '@/app/components/VideoCard';
import VideoPlayer from '@/app/components/VideoPlayer';
import RecommendedVideos from '@/app/components/RecommendedVideos';
import { formatCount, formatDuration } from '@/app/utils/formatters';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
}

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl?: string;
  embedUrl?: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: Category[];
  tags: string[];
  uploadedBy: string;
  isPublished: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function VideoPage({ params }: { params: Promise<{ slug: string }> }) {
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [video, setVideo] = useState<Video | null>(null);
  const [relatedVideos, setRelatedVideos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [slug, setSlug] = useState<string>('');

  const { data: session } = useSession();
  const router = useRouter();

  // Unwrap params Promise
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setSlug(resolvedParams.slug);
    };
    unwrapParams();
  }, [params]);

  // Fetch video data
  useEffect(() => {
    const fetchVideoData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/videos/${slug}`);

        if (response.data && response.data.video) {
          setVideo(response.data.video);

          // Increment view count
          await axios.post(`/api/videos/${slug}/view`);

          // Add to watch history if user is logged in
          if (session && session.user) {
            try {
              await axios.post('/api/profile/history', {
                videoId: response.data.video._id
              });
            } catch (historyError) {
              console.error('Error adding to watch history:', historyError);
              // Non-critical error, don't show to user
            }
          }

          // Fetch related videos based on categories
          if (response.data.video.categories && response.data.video.categories.length > 0) {
            const categoryIds = response.data.video.categories.map((cat: Category) => cat._id);
            const relatedResponse = await axios.get(`/api/videos?limit=6&categories=${categoryIds.join(',')}&exclude=${response.data.video._id}`);

            if (relatedResponse.data && relatedResponse.data.videos) {
              setRelatedVideos(relatedResponse.data.videos);
            }
          }
        } else {
          setError('Video not found');
        }
      } catch (error) {
        console.error('Error fetching video:', error);
        setError('Failed to load video');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchVideoData();
    }
  }, [slug, session]);

  // Check if video is in favorites
  useEffect(() => {
    if (session && video) {
      const checkFavoriteStatus = async () => {
        try {
          const response = await axios.get('/api/profile/favorites');

          if (response.data && response.data.favorites) {
            setIsFavorite(response.data.favorites.some((fav: any) => fav._id === video._id));
          }
        } catch (error) {
          console.error('Error checking favorite status:', error);
        }
      };

      checkFavoriteStatus();
    }
  }, [session, video]);

  const handleLike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isLiked) {
        // Unlike the video
        await axios.delete(`/api/videos/${video._id}/like`);
        setIsLiked(false);
        setVideo(prev => prev ? { ...prev, likes: prev.likes - 1 } : null);
      } else {
        // Like the video
        await axios.post(`/api/videos/${video._id}/like`);
        setIsLiked(true);

        // If it was previously disliked, decrement dislikes
        if (isDisliked) {
          setIsDisliked(false);
          setVideo(prev => prev ? {
            ...prev,
            likes: prev.likes + 1,
            dislikes: prev.dislikes - 1
          } : null);
        } else {
          setVideo(prev => prev ? { ...prev, likes: prev.likes + 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error liking video:', error);
    }
  };

  const handleDislike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isDisliked) {
        // Remove dislike
        await axios.delete(`/api/videos/${video._id}/dislike`);
        setIsDisliked(false);
        setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes - 1 } : null);
      } else {
        // Dislike the video
        await axios.post(`/api/videos/${video._id}/dislike`);
        setIsDisliked(true);

        // If it was previously liked, decrement likes
        if (isLiked) {
          setIsLiked(false);
          setVideo(prev => prev ? {
            ...prev,
            dislikes: prev.dislikes + 1,
            likes: prev.likes - 1
          } : null);
        } else {
          setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes + 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error disliking video:', error);
    }
  };

  const toggleFavorite = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isFavorite) {
        // Remove from favorites
        await axios.delete(`/api/profile/favorites?videoId=${video._id}`);
      } else {
        // Add to favorites
        await axios.post('/api/profile/favorites', {
          videoId: video._id
        });
      }

      setIsFavorite(!isFavorite);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-[60vh] flex items-center justify-center">
        <div className="text-gray-400 text-xl">Loading video...</div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="w-full min-h-[60vh] flex flex-col items-center justify-center">
        <div className="text-red-500 text-xl mb-4">{error || 'Video not found'}</div>
        <Link href="/" className="btn-primary">
          Return to Homepage
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Video Header with Gradient */}
      <div className="bg-gradient-to-r from-orange-500 via-red-500 to-pink-500 p-1">
        <div className="bg-black rounded-lg p-4">
          <h1 className="text-2xl md:text-3xl font-bold text-white mb-2 leading-tight">{video.title}</h1>
          <div className="flex flex-wrap items-center gap-4 text-sm">
            <div className="flex items-center text-orange-400">
              <FaEye className="mr-1" />
              <span className="font-semibold">{formatCount(video.views)}</span>
            </div>
            <div className="flex items-center text-green-400">
              <FaThumbsUp className="mr-1" />
              <span className="font-semibold">{video.likes}%</span>
            </div>
            <div className="flex items-center text-gray-400">
              <FaClock className="mr-1" />
              <span>{formatDuration(video.duration)}</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {video.tags?.slice(0, 5).map((tag, index) => (
                <span key={index} className="px-2 py-1 bg-gradient-to-r from-orange-500 to-red-500 text-white text-xs rounded-full">
                  {tag}
                </span>
              ))}
            </div>
          </div>
        </div>
      </div>

      <div className="flex flex-col xl:flex-row gap-6 p-4">
        {/* Video Player and Info */}
        <div className="xl:w-[70%]">
          {/* Video Player */}
          <div className="rounded-xl overflow-hidden mb-6 bg-black shadow-2xl border border-gray-700">
            <div className="w-full" style={{ aspectRatio: '16/9' }}>
              <VideoPlayer
                videoUrl={video.embedUrl || video.videoUrl}
                videoId={video._id}
                title={video.title}
                poster={video.thumbnailUrl}
                isEmbedded={!!video.embedUrl}
              />
            </div>
          </div>
          {/* Action Buttons */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-4 mb-6 border border-gray-700">
            <div className="flex flex-wrap justify-between items-center gap-4">
              <div className="flex flex-wrap gap-3">
                <button
                  className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 ${
                    isLiked
                      ? 'bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg shadow-green-500/30'
                      : 'bg-gradient-to-r from-gray-600 to-gray-700 text-gray-300 hover:from-green-500 hover:to-green-600 hover:text-white'
                  }`}
                  onClick={handleLike}
                >
                  <FaThumbsUp className="mr-2" />
                  <span className="font-semibold">{formatCount(video.likes)}</span>
                </button>

                <button
                  className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 ${
                    isDisliked
                      ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/30'
                      : 'bg-gradient-to-r from-gray-600 to-gray-700 text-gray-300 hover:from-red-500 hover:to-red-600 hover:text-white'
                  }`}
                  onClick={handleDislike}
                >
                  <FaThumbsDown className="mr-2" />
                  <span className="font-semibold">{formatCount(video.dislikes)}</span>
                </button>

                <button
                  className={`flex items-center px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 ${
                    isFavorite
                      ? 'bg-gradient-to-r from-pink-500 to-red-500 text-white shadow-lg shadow-pink-500/30'
                      : 'bg-gradient-to-r from-gray-600 to-gray-700 text-gray-300 hover:from-pink-500 hover:to-red-500 hover:text-white'
                  }`}
                  onClick={toggleFavorite}
                >
                  <FaHeart className="mr-2" />
                  <span className="font-semibold">{isFavorite ? 'Favorited' : 'Favorite'}</span>
                </button>

                <button className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-blue-500/30">
                  <FaShare className="mr-2" />
                  <span className="font-semibold">Share</span>
                </button>

                <button className="flex items-center px-4 py-2 bg-gradient-to-r from-orange-500 to-red-500 text-white rounded-full hover:from-orange-600 hover:to-red-600 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-orange-500/30">
                  <FaFlag className="mr-2" />
                  <span className="font-semibold">Report</span>
                </button>
              </div>

              <div className="flex items-center text-gray-400 space-x-6">
                <span className="flex items-center text-orange-400">
                  <FaCalendarAlt className="mr-2" />
                  {new Date(video.createdAt).toLocaleDateString()}
                </span>
              </div>
            </div>
          </div>

          {/* Categories and Tags */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-4 mb-6 border border-gray-700">
            <h3 className="text-lg font-semibold text-white mb-3 flex items-center">
              <FaTag className="mr-2 text-orange-400" />
              Categories & Tags
            </h3>

            <div className="flex flex-wrap gap-2 mb-4">
              {video.categories && video.categories.map((category) => (
                <Link
                  key={category._id}
                  href={`/categories/${category.slug}`}
                  className="bg-gradient-to-r from-orange-500 to-red-500 hover:from-orange-600 hover:to-red-600 text-white text-sm px-4 py-2 rounded-full transition-all duration-300 transform hover:scale-105 shadow-lg shadow-orange-500/30"
                >
                  {category.name}
                </Link>
              ))}
            </div>

            <div className="flex flex-wrap gap-2">
              {video.tags && video.tags.map((tag) => (
                <Link
                  key={tag}
                  href={`/search?q=${tag}`}
                  className="flex items-center text-sm bg-gray-700 hover:bg-gradient-to-r hover:from-blue-500 hover:to-purple-600 text-gray-300 hover:text-white px-3 py-1 rounded-full transition-all duration-300 transform hover:scale-105"
                >
                  <FaTag className="mr-1" /> {tag}
                </Link>
              ))}
            </div>
          </div>

          {/* Video Description */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-6 mb-8 border border-gray-700">
            <h3 className="text-xl font-semibold text-white mb-4 flex items-center">
              <FaInfoCircle className="mr-2 text-blue-400" />
              Description
            </h3>
            <p className="text-gray-300 whitespace-pre-line leading-relaxed">{video.description}</p>
          </div>

          {/* Comments Section */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl border border-gray-700 overflow-hidden">
            <CommentSection videoSlug={slug} videoId={video._id} />
          </div>
        </div>

        {/* Sidebar with Recommendations */}
        <div className="xl:w-[30%] space-y-6">
          {/* Related Videos based on categories */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-4 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <FaPlay className="mr-2 text-red-500" />
              Related Videos
            </h3>
            {relatedVideos.length > 0 ? (
              <div className="space-y-3">
                {relatedVideos.map((video) => (
                  <div key={video._id} className="group">
                    <VideoCard
                      id={video._id}
                      title={video.title}
                      thumbnail={video.thumbnailUrl}
                      duration={video.duration}
                      views={video.views}
                      likes={video.likes}
                      slug={video.slug}
                      isCompact={true}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-gray-400 text-center py-8">
                <FaExclamationTriangle className="mx-auto mb-2 text-2xl" />
                No related videos found
              </div>
            )}
          </div>

          {/* Personalized Recommendations */}
          <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-xl p-4 border border-gray-700">
            <h3 className="text-xl font-bold text-white mb-4 flex items-center">
              <FaHeart className="mr-2 text-pink-500" />
              You May Also Like
            </h3>
            <RecommendedVideos
              excludeVideoId={video?._id}
              limit={4}
              title=""
              isCompact={true}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
