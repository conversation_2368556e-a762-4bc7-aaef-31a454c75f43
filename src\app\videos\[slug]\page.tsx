'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FaEye, FaThumbsUp, FaThumbsDown, FaShare, FaFlag, FaTag, FaHeart, FaClock, FaCalendarAlt, FaInfoCircle, FaPlay, FaExclamationTriangle } from 'react-icons/fa';
import { useSession } from 'next-auth/react';
import CommentSection from '@/app/components/CommentSection';
import VideoCard from '@/app/components/VideoCard';
import VideoPlayer from '@/app/components/VideoPlayer';
import RecommendedVideos from '@/app/components/RecommendedVideos';
import { formatCount, formatDuration } from '@/app/utils/formatters';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
}

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl: string;
  tags: string[];
  categories: Category[];
  uploader: {
    _id: string;
    username: string;
    avatar?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function VideoPage({ params }: { params: Promise<{ slug: string }> }) {
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [video, setVideo] = useState<Video | null>(null);
  const [relatedVideos, setRelatedVideos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [slug, setSlug] = useState<string>('');

  const { data: session } = useSession();
  const router = useRouter();

  // Unwrap params Promise
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setSlug(resolvedParams.slug);
    };
    unwrapParams();
  }, [params]);

  // Fetch video data
  useEffect(() => {
    const fetchVideoData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/videos/${slug}`);

        if (response.data && response.data.video) {
          setVideo(response.data.video);

          // Increment view count
          await axios.post(`/api/videos/${slug}/view`);

          // Add to watch history if user is logged in
          if (session && session.user) {
            await axios.post('/api/profile/history', {
              videoId: response.data.video._id
            });
          }

          // Fetch related videos
          const relatedResponse = await axios.get(`/api/videos?category=${response.data.video.categories[0]?._id}&limit=4&exclude=${response.data.video._id}`);
          setRelatedVideos(relatedResponse.data.videos || []);
        } else {
          setError('Video not found');
        }
      } catch (error) {
        console.error('Error fetching video:', error);
        setError('Failed to load video');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchVideoData();
    }
  }, [slug, session]);

  // Check if user has liked/disliked/favorited this video
  useEffect(() => {
    const checkUserInteractions = async () => {
      if (!session || !video) return;

      try {
        // Check if liked
        const likeResponse = await axios.get(`/api/profile/likes?videoId=${video._id}`);
        setIsLiked(likeResponse.data.isLiked);

        // Check if disliked
        const dislikeResponse = await axios.get(`/api/profile/dislikes?videoId=${video._id}`);
        setIsDisliked(dislikeResponse.data.isDisliked);

        // Check if favorited
        const favoriteResponse = await axios.get(`/api/profile/favorites?videoId=${video._id}`);
        setIsFavorite(favoriteResponse.data.isFavorite);
      } catch (error) {
        console.error('Error checking user interactions:', error);
      }
    };

    checkUserInteractions();
  }, [session, video]);

  const handleLike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isLiked) {
        // Remove like
        await axios.delete(`/api/profile/likes?videoId=${video._id}`);
        setIsLiked(false);
        setVideo(prev => prev ? { ...prev, likes: prev.likes - 1 } : null);
      } else {
        // Add like
        await axios.post('/api/profile/likes', {
          videoId: video._id
        });
        setIsLiked(true);
        setVideo(prev => prev ? { ...prev, likes: prev.likes + 1 } : null);

        // Remove dislike if it exists
        if (isDisliked) {
          await axios.delete(`/api/profile/dislikes?videoId=${video._id}`);
          setIsDisliked(false);
          setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes - 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  const handleDislike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isDisliked) {
        // Remove dislike
        await axios.delete(`/api/profile/dislikes?videoId=${video._id}`);
        setIsDisliked(false);
        setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes - 1 } : null);
      } else {
        // Add dislike
        await axios.post('/api/profile/dislikes', {
          videoId: video._id
        });
        setIsDisliked(true);
        setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes + 1 } : null);

        // Remove like if it exists
        if (isLiked) {
          await axios.delete(`/api/profile/likes?videoId=${video._id}`);
          setIsLiked(false);
          setVideo(prev => prev ? { ...prev, likes: prev.likes - 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error toggling dislike:', error);
    }
  };

  const toggleFavorite = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isFavorite) {
        // Remove from favorites
        await axios.delete(`/api/profile/favorites?videoId=${video._id}`);
      } else {
        // Add to favorites
        await axios.post('/api/profile/favorites', {
          videoId: video._id
        });
      }

      setIsFavorite(!isFavorite);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-[60vh] flex items-center justify-center">
        <div className="text-gray-400 text-xl">Loading video...</div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="w-full min-h-[60vh] flex flex-col items-center justify-center">
        <div className="text-red-500 text-xl mb-4">{error || 'Video not found'}</div>
        <Link href="/" className="btn-primary">
          Return to Homepage
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900">
      <div className="container mx-auto px-4 py-6 max-w-7xl">
        {/* Video Player Section */}
        <div className="mb-8">
          <div className="relative rounded-2xl overflow-hidden shadow-2xl bg-black">
            <VideoPlayer
              videoUrl={video.embedUrl || video.videoUrl}
              videoId={video._id}
              title={video.title}
              poster={video.thumbnailUrl}
              isEmbedded={!!video.embedUrl}
            />
            {/* Gradient overlay for modern effect */}
            <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent pointer-events-none"></div>
          </div>
        </div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-8">
          {/* Left Column - Video Info */}
          <div className="xl:col-span-3 space-y-6">
            {/* Video Title & Stats Card */}
            <div className="bg-gradient-to-r from-gray-800/50 to-gray-900/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
              <h1 className="text-2xl md:text-3xl lg:text-4xl font-bold text-white mb-4 leading-tight bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                {video.title}
              </h1>

              {/* Modern Stats Bar */}
              <div className="flex flex-wrap items-center gap-6 text-sm">
                <div className="flex items-center gap-2 bg-orange-500/10 px-3 py-2 rounded-full border border-orange-500/20">
                  <FaEye className="text-orange-400" />
                  <span className="text-orange-300 font-medium">{formatCount(video.views)} views</span>
                </div>
                <div className="flex items-center gap-2 bg-blue-500/10 px-3 py-2 rounded-full border border-blue-500/20">
                  <FaClock className="text-blue-400" />
                  <span className="text-blue-300 font-medium">{formatDuration(video.duration)}</span>
                </div>
                <div className="flex items-center gap-2 bg-purple-500/10 px-3 py-2 rounded-full border border-purple-500/20">
                  <FaCalendarAlt className="text-purple-400" />
                  <span className="text-purple-300 font-medium">{new Date(video.createdAt).toLocaleDateString()}</span>
                </div>
              </div>
            </div>

            {/* Modern Action Buttons */}
            <div className="bg-gradient-to-r from-gray-800/30 to-gray-900/30 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30">
              <div className="flex flex-wrap items-center gap-3">
                <button
                  onClick={handleLike}
                  className={`group flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 min-h-[48px] transform hover:scale-105 ${
                    isLiked
                      ? 'bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg shadow-orange-500/25'
                      : 'bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-300 hover:from-orange-500/20 hover:to-orange-600/20 hover:text-orange-300 border border-gray-600/50 hover:border-orange-500/50'
                  }`}
                >
                  <FaThumbsUp className="transition-transform group-hover:scale-110" />
                  <span className="font-medium">{formatCount(video.likes)}</span>
                </button>

                <button
                  onClick={handleDislike}
                  className={`group flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 min-h-[48px] transform hover:scale-105 ${
                    isDisliked
                      ? 'bg-gradient-to-r from-red-500 to-red-600 text-white shadow-lg shadow-red-500/25'
                      : 'bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-300 hover:from-red-500/20 hover:to-red-600/20 hover:text-red-300 border border-gray-600/50 hover:border-red-500/50'
                  }`}
                >
                  <FaThumbsDown className="transition-transform group-hover:scale-110" />
                  <span className="font-medium">{formatCount(video.dislikes)}</span>
                </button>

                <button
                  onClick={toggleFavorite}
                  className={`group flex items-center gap-3 px-6 py-3 rounded-xl transition-all duration-300 min-h-[48px] transform hover:scale-105 ${
                    isFavorite
                      ? 'bg-gradient-to-r from-pink-500 to-pink-600 text-white shadow-lg shadow-pink-500/25'
                      : 'bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-300 hover:from-pink-500/20 hover:to-pink-600/20 hover:text-pink-300 border border-gray-600/50 hover:border-pink-500/50'
                  }`}
                >
                  <FaHeart className="transition-transform group-hover:scale-110" />
                  <span className="font-medium">Favorite</span>
                </button>

                <button className="group flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-300 hover:from-blue-500/20 hover:to-blue-600/20 hover:text-blue-300 border border-gray-600/50 hover:border-blue-500/50 rounded-xl transition-all duration-300 min-h-[48px] transform hover:scale-105">
                  <FaShare className="transition-transform group-hover:scale-110" />
                  <span className="font-medium">Share</span>
                </button>

                <button className="group flex items-center gap-3 px-6 py-3 bg-gradient-to-r from-gray-700/50 to-gray-800/50 text-gray-300 hover:from-yellow-500/20 hover:to-yellow-600/20 hover:text-yellow-300 border border-gray-600/50 hover:border-yellow-500/50 rounded-xl transition-all duration-300 min-h-[48px] transform hover:scale-105">
                  <FaFlag className="transition-transform group-hover:scale-110" />
                  <span className="font-medium">Report</span>
                </button>
              </div>
            </div>

            {/* Modern Uploader Card */}
            <div className="bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="relative">
                    <div className="w-16 h-16 bg-gradient-to-br from-orange-400 to-orange-600 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                      {video.uploader.username.charAt(0).toUpperCase()}
                    </div>
                    <div className="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-900 flex items-center justify-center">
                      <div className="w-2 h-2 bg-white rounded-full"></div>
                    </div>
                  </div>
                  <div>
                    <h3 className="text-white font-bold text-lg">{video.uploader.username}</h3>
                    <p className="text-gray-400 text-sm">Content Creator • Online</p>
                    <div className="flex items-center gap-2 mt-1">
                      <div className="flex text-yellow-400">
                        {'★'.repeat(5)}
                      </div>
                      <span className="text-gray-400 text-xs">4.9 rating</span>
                    </div>
                  </div>
                </div>
                <button className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-6 py-3 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 shadow-lg shadow-orange-500/25">
                  Subscribe
                </button>
              </div>
            </div>

            {/* Modern Description Card */}
            {video.description && (
              <div className="bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30">
                <h3 className="text-white font-bold text-lg mb-4 flex items-center gap-3">
                  <div className="w-8 h-8 bg-gradient-to-br from-blue-400 to-blue-600 rounded-lg flex items-center justify-center">
                    <FaInfoCircle className="text-white text-sm" />
                  </div>
                  About this video
                </h3>
                <div className="bg-gray-800/50 rounded-xl p-4 border border-gray-700/50">
                  <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                    {video.description}
                  </p>
                </div>
              </div>
            )}

            {/* Modern Tags and Categories */}
            <div className="bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30">
              <h3 className="text-white font-bold text-lg mb-4 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-purple-400 to-purple-600 rounded-lg flex items-center justify-center">
                  <FaTag className="text-white text-sm" />
                </div>
                Tags & Categories
              </h3>

              {/* Categories */}
              {video.categories && video.categories.length > 0 && (
                <div className="mb-6">
                  <h4 className="text-gray-400 text-sm mb-3 font-medium">Categories</h4>
                  <div className="flex flex-wrap gap-3">
                    {video.categories.map((category) => (
                      <Link
                        key={category._id}
                        href={`/categories/${category.slug}`}
                        className="group bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-4 py-2 rounded-xl text-sm font-medium transition-all duration-300 transform hover:scale-105 shadow-lg shadow-orange-500/25"
                      >
                        <span className="flex items-center gap-2">
                          {category.name}
                          <span className="opacity-0 group-hover:opacity-100 transition-opacity">→</span>
                        </span>
                      </Link>
                    ))}
                  </div>
                </div>
              )}

              {/* Tags */}
              {video.tags && video.tags.length > 0 && (
                <div>
                  <h4 className="text-gray-400 text-sm mb-3 font-medium">Tags</h4>
                  <div className="flex flex-wrap gap-2">
                    {video.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-gradient-to-r from-gray-700/60 to-gray-800/60 hover:from-purple-500/20 hover:to-purple-600/20 text-gray-300 hover:text-purple-300 px-3 py-2 rounded-lg text-sm transition-all duration-300 cursor-pointer border border-gray-600/50 hover:border-purple-500/50 backdrop-blur-sm"
                      >
                        #{tag}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </div>

            {/* Modern Comments Section */}
            <div className="bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30">
              <h3 className="text-white font-bold text-lg mb-4 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-green-400 to-green-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm font-bold">💬</span>
                </div>
                Comments
              </h3>
              <CommentSection videoId={video._id} />
            </div>
          </div>

          {/* Right Column - Related Videos */}
          <div className="xl:col-span-1">
            <div className="bg-gradient-to-r from-gray-800/40 to-gray-900/40 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/30 sticky top-6">
              <h3 className="text-white font-bold text-lg mb-6 flex items-center gap-3">
                <div className="w-8 h-8 bg-gradient-to-br from-pink-400 to-pink-600 rounded-lg flex items-center justify-center">
                  <span className="text-white text-sm">🎬</span>
                </div>
                Related Videos
              </h3>

              <div className="space-y-4 max-h-[600px] overflow-y-auto custom-scrollbar">
                {relatedVideos.map((relatedVideo) => (
                  <div key={relatedVideo._id} className="group">
                    <VideoCard
                      video={relatedVideo}
                      layout="list"
                    />
                  </div>
                ))}
              </div>

              {relatedVideos.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🎥</span>
                  </div>
                  <p className="text-gray-400">No related videos found</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
