'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { FaEye, FaThumbsUp, FaThumbsDown, FaShare, FaFlag, FaTag, FaHeart, FaClock, FaCalendarAlt, FaInfoCircle, FaPlay, FaExclamationTriangle } from 'react-icons/fa';
import { useSession } from 'next-auth/react';
import CommentSection from '@/app/components/CommentSection';
import VideoCard from '@/app/components/VideoCard';
import VideoPlayer from '@/app/components/VideoPlayer';
import RecommendedVideos from '@/app/components/RecommendedVideos';
import { formatCount, formatDuration } from '@/app/utils/formatters';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
}

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl: string;
  tags: string[];
  categories: Category[];
  uploader: {
    _id: string;
    username: string;
    avatar?: string;
  };
  createdAt: string;
  updatedAt: string;
}

export default function VideoPage({ params }: { params: Promise<{ slug: string }> }) {
  const [isLiked, setIsLiked] = useState(false);
  const [isDisliked, setIsDisliked] = useState(false);
  const [isFavorite, setIsFavorite] = useState(false);
  const [video, setVideo] = useState<Video | null>(null);
  const [relatedVideos, setRelatedVideos] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [slug, setSlug] = useState<string>('');

  const { data: session } = useSession();
  const router = useRouter();

  // Unwrap params Promise
  useEffect(() => {
    const unwrapParams = async () => {
      const resolvedParams = await params;
      setSlug(resolvedParams.slug);
    };
    unwrapParams();
  }, [params]);

  // Fetch video data
  useEffect(() => {
    const fetchVideoData = async () => {
      try {
        setLoading(true);
        const response = await axios.get(`/api/videos/${slug}`);

        if (response.data && response.data.video) {
          setVideo(response.data.video);

          // Increment view count
          await axios.post(`/api/videos/${slug}/view`);

          // Add to watch history if user is logged in
          if (session && session.user) {
            await axios.post('/api/profile/history', {
              videoId: response.data.video._id
            });
          }

          // Fetch related videos
          const relatedResponse = await axios.get(`/api/videos?category=${response.data.video.categories[0]?._id}&limit=4&exclude=${response.data.video._id}`);
          setRelatedVideos(relatedResponse.data.videos || []);
        } else {
          setError('Video not found');
        }
      } catch (error) {
        console.error('Error fetching video:', error);
        setError('Failed to load video');
      } finally {
        setLoading(false);
      }
    };

    if (slug) {
      fetchVideoData();
    }
  }, [slug, session]);

  // Check if user has liked/disliked/favorited this video
  useEffect(() => {
    const checkUserInteractions = async () => {
      if (!session || !video) return;

      try {
        // Check if liked
        const likeResponse = await axios.get(`/api/profile/likes?videoId=${video._id}`);
        setIsLiked(likeResponse.data.isLiked);

        // Check if disliked
        const dislikeResponse = await axios.get(`/api/profile/dislikes?videoId=${video._id}`);
        setIsDisliked(dislikeResponse.data.isDisliked);

        // Check if favorited
        const favoriteResponse = await axios.get(`/api/profile/favorites?videoId=${video._id}`);
        setIsFavorite(favoriteResponse.data.isFavorite);
      } catch (error) {
        console.error('Error checking user interactions:', error);
      }
    };

    checkUserInteractions();
  }, [session, video]);

  const handleLike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isLiked) {
        // Remove like
        await axios.delete(`/api/profile/likes?videoId=${video._id}`);
        setIsLiked(false);
        setVideo(prev => prev ? { ...prev, likes: prev.likes - 1 } : null);
      } else {
        // Add like
        await axios.post('/api/profile/likes', {
          videoId: video._id
        });
        setIsLiked(true);
        setVideo(prev => prev ? { ...prev, likes: prev.likes + 1 } : null);

        // Remove dislike if it exists
        if (isDisliked) {
          await axios.delete(`/api/profile/dislikes?videoId=${video._id}`);
          setIsDisliked(false);
          setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes - 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error toggling like:', error);
    }
  };

  const handleDislike = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isDisliked) {
        // Remove dislike
        await axios.delete(`/api/profile/dislikes?videoId=${video._id}`);
        setIsDisliked(false);
        setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes - 1 } : null);
      } else {
        // Add dislike
        await axios.post('/api/profile/dislikes', {
          videoId: video._id
        });
        setIsDisliked(true);
        setVideo(prev => prev ? { ...prev, dislikes: prev.dislikes + 1 } : null);

        // Remove like if it exists
        if (isLiked) {
          await axios.delete(`/api/profile/likes?videoId=${video._id}`);
          setIsLiked(false);
          setVideo(prev => prev ? { ...prev, likes: prev.likes - 1 } : null);
        }
      }
    } catch (error) {
      console.error('Error toggling dislike:', error);
    }
  };

  const toggleFavorite = async () => {
    if (!session || !video) {
      router.push('/auth/login?redirect=' + encodeURIComponent(`/videos/${slug}`));
      return;
    }

    try {
      if (isFavorite) {
        // Remove from favorites
        await axios.delete(`/api/profile/favorites?videoId=${video._id}`);
      } else {
        // Add to favorites
        await axios.post('/api/profile/favorites', {
          videoId: video._id
        });
      }

      setIsFavorite(!isFavorite);
    } catch (error) {
      console.error('Error toggling favorite:', error);
    }
  };

  if (loading) {
    return (
      <div className="w-full min-h-[60vh] flex items-center justify-center">
        <div className="text-gray-400 text-xl">Loading video...</div>
      </div>
    );
  }

  if (error || !video) {
    return (
      <div className="w-full min-h-[60vh] flex flex-col items-center justify-center">
        <div className="text-red-500 text-xl mb-4">{error || 'Video not found'}</div>
        <Link href="/" className="btn-primary">
          Return to Homepage
        </Link>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-ph-black-950">
      <div className="container-ph py-4">
        <div className="mb-4">
          <h1 className="text-xl md:text-2xl lg:text-3xl font-bold text-white mb-3 leading-tight">{video.title}</h1>
          
          <div className="flex flex-wrap items-center gap-4 text-sm text-gray-400">
            <div className="flex items-center gap-1">
              <FaEye className="text-ph-orange" />
              <span>{formatCount(video.views)} views</span>
            </div>
            <div className="flex items-center gap-1">
              <FaClock className="text-ph-orange" />
              <span>{formatDuration(video.duration)}</span>
            </div>
            <div className="flex items-center gap-1">
              <FaCalendarAlt className="text-ph-orange" />
              <span>{new Date(video.createdAt).toLocaleDateString()}</span>
            </div>
          </div>
        </div>

        <div className="mb-6">
          <div className="video-player-mobile">
            <VideoPlayer
              videoUrl={video.embedUrl || video.videoUrl}
              videoId={video._id}
              title={video.title}
              poster={video.thumbnailUrl}
              isEmbedded={!!video.embedUrl}
            />
          </div>
        </div>

        <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-6">
          <button
            onClick={handleLike}
            className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg transition-colors min-h-[44px] ${
              isLiked
                ? 'bg-ph-orange text-white'
                : 'bg-ph-black-800 text-gray-300 hover:bg-ph-black-700'
            }`}
          >
            <FaThumbsUp />
            <span className="text-sm sm:text-base">{formatCount(video.likes)}</span>
          </button>
          
          <button
            onClick={handleDislike}
            className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg transition-colors min-h-[44px] ${
              isDisliked
                ? 'bg-red-600 text-white'
                : 'bg-ph-black-800 text-gray-300 hover:bg-ph-black-700'
            }`}
          >
            <FaThumbsDown />
            <span className="text-sm sm:text-base">{formatCount(video.dislikes)}</span>
          </button>

          <button
            onClick={toggleFavorite}
            className={`flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 rounded-lg transition-colors min-h-[44px] ${
              isFavorite
                ? 'bg-pink-600 text-white'
                : 'bg-ph-black-800 text-gray-300 hover:bg-ph-black-700'
            }`}
          >
            <FaHeart />
            <span className="text-sm sm:text-base">Favorite</span>
          </button>

          <button className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-ph-black-800 text-gray-300 hover:bg-ph-black-700 rounded-lg transition-colors min-h-[44px]">
            <FaShare />
            <span className="text-sm sm:text-base">Share</span>
          </button>

          <button className="flex items-center gap-1 sm:gap-2 px-3 sm:px-4 py-2 bg-ph-black-800 text-gray-300 hover:bg-ph-black-700 rounded-lg transition-colors min-h-[44px]">
            <FaFlag />
            <span className="text-sm sm:text-base">Report</span>
          </button>
        </div>
      </div>
    </div>
  );
}
