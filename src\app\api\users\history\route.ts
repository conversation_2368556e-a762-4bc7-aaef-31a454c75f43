import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import User from '@/app/models/User';
import Video from '@/app/models/Video';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';

// GET /api/users/history - Get user watch history
export async function GET(req: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Get user with populated watch history
    const user = await User.findById((session.user as any).id)
      .populate({
        path: 'watchHistory',
        select: 'title thumbnail duration views slug categories',
        populate: {
          path: 'categories',
          select: 'name slug'
        }
      });
    
    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json(user.watchHistory);
  } catch (error: any) {
    console.error('Error fetching watch history:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to fetch watch history' },
      { status: 500 }
    );
  }
}

// POST /api/users/history - Add video to watch history
export async function POST(req: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    const { videoId } = await req.json();
    
    if (!videoId) {
      return NextResponse.json(
        { error: 'Video ID is required' },
        { status: 400 }
      );
    }
    
    // Check if video exists
    const video = await Video.findById(videoId);
    
    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }
    
    // Add to watch history (remove if exists and add to the beginning)
    const user = await User.findByIdAndUpdate(
      (session.user as any).id,
      { 
        $pull: { watchHistory: videoId } 
      }
    );
    
    await User.findByIdAndUpdate(
      (session.user as any).id,
      { 
        $push: { watchHistory: { $each: [videoId], $position: 0 } } 
      }
    );
    
    // Increment video views
    await Video.findByIdAndUpdate(
      videoId,
      { $inc: { views: 1 } }
    );
    
    return NextResponse.json({ success: true, message: 'Added to watch history' });
  } catch (error: any) {
    console.error('Error adding to watch history:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to add to watch history' },
      { status: 500 }
    );
  }
}

// DELETE /api/users/history - Clear watch history
export async function DELETE(req: NextRequest) {
  try {
    await dbConnect();
    
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    // Clear watch history
    await User.findByIdAndUpdate(
      (session.user as any).id,
      { $set: { watchHistory: [] } }
    );
    
    return NextResponse.json({ success: true, message: 'Watch history cleared' });
  } catch (error: any) {
    console.error('Error clearing watch history:', error);
    return NextResponse.json(
      { error: error.message || 'Failed to clear watch history' },
      { status: 500 }
    );
  }
}
