import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/mongodb-debug';

export async function GET(req: NextRequest) {
  try {
    // Try to connect to the database
    console.log('API Test route: Attempting to connect to MongoDB...');
    await dbConnect();
    console.log('API Test route: MongoDB connection successful');
    
    // Return success response
    return NextResponse.json({
      status: 'success',
      message: 'MongoDB connection successful',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('API Test route: MongoDB connection failed:', error);
    
    // Return error response
    return NextResponse.json({
      status: 'error',
      message: 'MongoDB connection failed',
      error: error instanceof Error ? error.message : String(error),
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
