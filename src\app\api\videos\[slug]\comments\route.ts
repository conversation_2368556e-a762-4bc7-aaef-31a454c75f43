import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';
import Comment from '@/app/models/Comment';

export async function GET(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const slug = params.slug;
    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const sort = searchParams.get('sort') || 'newest';

    // Calculate skip for pagination
    const skip = (page - 1) * limit;

    // Connect to the database
    await dbConnect();

    // Find video by slug
    const video = await Video.findOne({ slug, isActive: true });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Build sort options
    let sortOptions: any = {};
    switch (sort) {
      case 'newest':
        sortOptions = { createdAt: -1 };
        break;
      case 'oldest':
        sortOptions = { createdAt: 1 };
        break;
      case 'most-liked':
        sortOptions = { likes: -1 };
        break;
      default:
        sortOptions = { createdAt: -1 };
    }

    // Find comments for this video
    const comments = await Comment.find({
      video: video._id,
      isActive: true
    })
      .sort(sortOptions)
      .skip(skip)
      .limit(limit)
      .populate('user', 'username profilePicture');

    // Get total count for pagination
    const total = await Comment.countDocuments({
      video: video._id,
      isActive: true
    });

    // Calculate total pages
    const totalPages = Math.ceil(total / limit);

    return NextResponse.json({
      comments,
      pagination: {
        total,
        page,
        limit,
        totalPages,
        hasNextPage: page < totalPages,
        hasPrevPage: page > 1
      }
    });
  } catch (error) {
    console.error('Error fetching comments:', error);
    return NextResponse.json(
      { error: 'Failed to fetch comments' },
      { status: 500 }
    );
  }
}

export async function POST(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const slug = params.slug;
    const { content } = await req.json();

    // Get user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to comment' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find video by slug
    const video = await Video.findOne({ slug, isActive: true });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Find user
    const user = await import('@/app/models/User').then(module =>
      module.default.findOne({ email: session.user.email })
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Create new comment
    const comment = await Comment.create({
      video: video._id,
      user: user._id,
      content
    });

    // Populate user data
    await comment.populate('user', 'username profilePicture');

    return NextResponse.json(comment, { status: 201 });
  } catch (error) {
    console.error('Error creating comment:', error);
    return NextResponse.json(
      { error: 'Failed to create comment' },
      { status: 500 }
    );
  }
}
