import mongoose, { Schema, Document } from 'mongoose';

export interface IVideo extends Document {
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: mongoose.Types.ObjectId[];
  tags: string[];
  uploadedBy: mongoose.Types.ObjectId;
  isPublished: boolean;
  isVerified: boolean;
  isEmbedded?: boolean;
  createdAt: Date;
  updatedAt: Date;
}

const VideoSchema: Schema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Title is required'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters'],
    },
    description: {
      type: String,
      required: [true, 'Description is required'],
      trim: true,
      maxlength: [5000, 'Description cannot be more than 5000 characters'],
    },
    slug: {
      type: String,
      required: [true, 'Slug is required'],
      unique: true,
      lowercase: true,
      trim: true,
    },
    duration: {
      type: Number,
      required: [true, 'Duration is required'],
      min: [1, 'Duration must be at least 1 second'],
    },
    views: {
      type: Number,
      default: 0,
    },
    likes: {
      type: Number,
      default: 0,
    },
    dislikes: {
      type: Number,
      default: 0,
    },
    thumbnailUrl: {
      type: String,
      required: [true, 'Thumbnail URL is required'],
    },
    videoUrl: {
      type: String,
      required: [true, 'Video URL is required'],
    },
    sourceUrl: {
      type: String,
      default: '',
    },
    sourceSite: {
      type: String,
      default: '',
    },
    categories: [{
      type: Schema.Types.ObjectId,
      ref: 'Category',
      required: [true, 'At least one category is required'],
    }],
    tags: [{
      type: String,
      trim: true,
    }],
    uploadedBy: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: [true, 'Uploader information is required'],
    },
    isPublished: {
      type: Boolean,
      default: false,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
    isEmbedded: {
      type: Boolean,
      default: false,
    },
  },
  {
    timestamps: true,
  }
);

// Create a text index for search functionality
VideoSchema.index({ title: 'text', description: 'text', tags: 'text' });

// Generate a slug from the title
VideoSchema.pre('save', function(next) {
  if (this.isModified('title') || !this.slug) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^\w\s]/gi, '')
      .replace(/\s+/g, '-')
      .concat('-', Date.now().toString().slice(-4));
  }
  next();
});

// Check if model exists before creating a new one (for Next.js hot reloading)
export default mongoose.models.Video || mongoose.model<IVideo>('Video', VideoSchema);
