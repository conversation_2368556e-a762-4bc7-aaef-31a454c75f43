// This script sets up the initial database with admin user and categories
const { exec } = require('child_process');
const path = require('path');

console.log('Starting database setup...');

// Run create-admin.js
console.log('\n=== Creating admin user ===');
exec('node ' + path.join(__dirname, 'create-admin.js'), (error, stdout, stderr) => {
  if (error) {
    console.error(`Error creating admin user: ${error.message}`);
    return;
  }
  if (stderr) {
    console.error(`Admin user creation stderr: ${stderr}`);
  }
  console.log(stdout);
  
  // After admin is created, create categories
  console.log('\n=== Creating categories ===');
  exec('node ' + path.join(__dirname, 'create-categories.js'), (error, stdout, stderr) => {
    if (error) {
      console.error(`Error creating categories: ${error.message}`);
      return;
    }
    if (stderr) {
      console.error(`Categories creation stderr: ${stderr}`);
    }
    console.log(stdout);
    
    console.log('\n=== Database setup completed ===');
    console.log('You can now log in with:');
    console.log('Email: ' + (process.env.ADMIN_EMAIL || '<EMAIL>'));
    console.log('Password: ' + (process.env.ADMIN_PASSWORD || 'admin123'));
  });
});
