import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';

export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Await params to get the slug
    const { slug } = await params;

    // Connect to the database
    await dbConnect();

    // Find video by slug
    const video = await Video.findOne({ slug, isActive: true })
      .populate('categories', 'name slug');

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Don't increment view count here, we'll do it in a separate endpoint

    // Find related videos
    const relatedVideos = await Video.find({
      _id: { $ne: video._id },
      $or: [
        { categories: { $in: video.categories } },
        { tags: { $in: video.tags } }
      ],
      isActive: true
    })
      .limit(6)
      .sort({ views: -1 })
      .select('title thumbnail duration views likes slug');

    return NextResponse.json({ video, relatedVideos });
  } catch (error) {
    console.error('Error fetching video:', error);
    return NextResponse.json(
      { error: 'Failed to fetch video' },
      { status: 500 }
    );
  }
}

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ slug: string }> }
) {
  try {
    // Await params to get the slug
    const { slug } = await params;
    const body = await req.json();

    // Connect to the database
    await dbConnect();

    // Find and update video
    const video = await Video.findOneAndUpdate(
      { slug },
      body,
      { new: true, runValidators: true }
    );

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(video);
  } catch (error) {
    console.error('Error updating video:', error);
    return NextResponse.json(
      { error: 'Failed to update video' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const slug = params.slug;

    // Connect to the database
    await dbConnect();

    // Find and delete video
    const video = await Video.findOneAndDelete({ slug });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({ message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    return NextResponse.json(
      { error: 'Failed to delete video' },
      { status: 500 }
    );
  }
}
