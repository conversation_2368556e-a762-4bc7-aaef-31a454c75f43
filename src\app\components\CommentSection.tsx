'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { FaUser, FaThumbsUp, FaThumbsDown, FaEdit, FaTrash, FaReply } from 'react-icons/fa';
import Link from 'next/link';

interface Comment {
  _id: string;
  content: string;
  user: {
    _id: string;
    username: string;
    profilePicture?: string;
  };
  likes: number;
  dislikes: number;
  createdAt: string;
}

interface CommentSectionProps {
  videoSlug: string;
  videoId: string;
}

export default function CommentSection({ videoSlug, videoId }: CommentSectionProps) {
  const { data: session } = useSession();
  const [comments, setComments] = useState<Comment[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [newComment, setNewComment] = useState('');
  const [submitting, setSubmitting] = useState(false);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');

  // Fetch comments
  useEffect(() => {
    const fetchComments = async () => {
      try {
        setLoading(true);
        const response = await fetch(`/api/videos/${videoSlug}/comments?page=${page}&limit=10`);
        const data = await response.json();

        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch comments');
        }

        setComments(data.comments || []);
        setTotalPages(data.pagination?.totalPages || 1);
      } catch (error: any) {
        setError(error.message || 'An error occurred while fetching comments');
        console.error('Error fetching comments:', error);
      } finally {
        setLoading(false);
      }
    };

    if (videoSlug) {
      fetchComments();
    }
  }, [videoSlug, page]);

  // Submit new comment
  const handleSubmitComment = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!session) {
      alert('You must be logged in to comment');
      return;
    }

    if (!newComment.trim()) {
      return;
    }

    try {
      setSubmitting(true);

      const response = await fetch(`/api/comments`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: newComment,
          video: videoId
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit comment');
      }

      // Add new comment to the list with user info from session
      const newCommentWithUser = {
        ...data,
        user: {
          _id: (session.user as any).id,
          username: (session.user as any).username || session.user?.name || 'User',
          profilePicture: (session.user as any).image
        }
      };

      setComments([newCommentWithUser, ...comments]);
      setNewComment('');
    } catch (error: any) {
      alert(error.message || 'An error occurred while submitting your comment');
      console.error('Error submitting comment:', error);
    } finally {
      setSubmitting(false);
    }
  };

  // Edit comment
  const handleEditComment = (comment: Comment) => {
    setEditingComment(comment._id);
    setEditContent(comment.content);
  };

  // Update comment
  const handleUpdateComment = async (commentId: string) => {
    if (!editContent.trim()) {
      return;
    }

    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          content: editContent
        })
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to update comment');
      }

      // Update comment in the list
      setComments(comments.map(comment =>
        comment._id === commentId ? { ...comment, content: editContent } : comment
      ));
      setEditingComment(null);
    } catch (error: any) {
      alert(error.message || 'An error occurred while updating your comment');
      console.error('Error updating comment:', error);
    }
  };

  // Delete comment
  const handleDeleteComment = async (commentId: string) => {
    if (!confirm('Are you sure you want to delete this comment?')) {
      return;
    }

    try {
      const response = await fetch(`/api/comments/${commentId}`, {
        method: 'DELETE'
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to delete comment');
      }

      // Remove comment from the list
      setComments(comments.filter(comment => comment._id !== commentId));
    } catch (error: any) {
      alert(error.message || 'An error occurred while deleting your comment');
      console.error('Error deleting comment:', error);
    }
  };

  // Format date
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="mt-8">
      <h3 className="text-xl font-bold text-white mb-4">Comments ({comments.length})</h3>

      {/* Comment form */}
      {session ? (
        <form onSubmit={handleSubmitComment} className="mb-6">
          <div className="flex items-start gap-4">
            <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0">
              <FaUser className="text-gray-400" />
            </div>
            <div className="flex-grow">
              <textarea
                className="w-full bg-gray-800 text-white rounded-lg p-3 min-h-[100px] border border-gray-700 focus:border-red-500 focus:outline-none"
                placeholder="Add a comment..."
                value={newComment}
                onChange={(e) => setNewComment(e.target.value)}
                disabled={submitting}
              ></textarea>
              <div className="flex justify-end mt-2">
                <button
                  type="submit"
                  className="btn-primary"
                  disabled={submitting || !newComment.trim()}
                >
                  {submitting ? 'Posting...' : 'Post Comment'}
                </button>
              </div>
            </div>
          </div>
        </form>
      ) : (
        <div className="bg-gray-800 p-4 rounded-lg mb-6 text-center">
          <p className="text-gray-300 mb-2">You need to be logged in to comment</p>
          <Link href="/auth/login" className="btn-primary inline-block">
            Login to Comment
          </Link>
        </div>
      )}

      {/* Comments list */}
      {loading ? (
        <div className="text-center py-8">
          <p className="text-gray-400">Loading comments...</p>
        </div>
      ) : error ? (
        <div className="bg-red-900 text-red-200 p-4 rounded-lg">
          <p>{error}</p>
        </div>
      ) : comments.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-400">No comments yet. Be the first to comment!</p>
        </div>
      ) : (
        <div className="space-y-6">
          {comments.map((comment) => (
            <div key={comment._id} className="bg-gray-800 rounded-lg p-4">
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0">
                  {comment.user.profilePicture ? (
                    <img
                      src={comment.user.profilePicture}
                      alt={comment.user.username}
                      className="w-full h-full rounded-full object-cover"
                    />
                  ) : (
                    <FaUser className="text-gray-400" />
                  )}
                </div>
                <div className="flex-grow">
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="text-white font-medium">{comment.user.username}</h4>
                      <p className="text-gray-400 text-sm">{formatDate(comment.createdAt)}</p>
                    </div>
                    {session && (session.user as any).id === comment.user._id && (
                      <div className="flex gap-2">
                        <button
                          onClick={() => handleEditComment(comment)}
                          className="text-gray-400 hover:text-white"
                          title="Edit"
                        >
                          <FaEdit />
                        </button>
                        <button
                          onClick={() => handleDeleteComment(comment._id)}
                          className="text-gray-400 hover:text-red-500"
                          title="Delete"
                        >
                          <FaTrash />
                        </button>
                      </div>
                    )}
                  </div>

                  {editingComment === comment._id ? (
                    <div className="mt-2">
                      <textarea
                        className="w-full bg-gray-700 text-white rounded-lg p-3 min-h-[80px] border border-gray-600 focus:border-red-500 focus:outline-none"
                        value={editContent}
                        onChange={(e) => setEditContent(e.target.value)}
                      ></textarea>
                      <div className="flex justify-end mt-2 gap-2">
                        <button
                          onClick={() => setEditingComment(null)}
                          className="btn-secondary"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={() => handleUpdateComment(comment._id)}
                          className="btn-primary"
                          disabled={!editContent.trim()}
                        >
                          Update
                        </button>
                      </div>
                    </div>
                  ) : (
                    <p className="text-white mt-2">{comment.content}</p>
                  )}

                  <div className="flex items-center gap-4 mt-3">
                    <button className="flex items-center gap-1 text-gray-400 hover:text-white">
                      <FaThumbsUp /> <span>{comment.likes}</span>
                    </button>
                    <button className="flex items-center gap-1 text-gray-400 hover:text-white">
                      <FaThumbsDown /> <span>{comment.dislikes}</span>
                    </button>
                    <button className="flex items-center gap-1 text-gray-400 hover:text-white">
                      <FaReply /> <span>Reply</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            <button
              onClick={() => setPage(p => Math.max(1, p - 1))}
              disabled={page === 1}
              className={`px-4 py-2 rounded-lg ${
                page === 1
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              Previous
            </button>

            {Array.from({ length: totalPages }, (_, i) => i + 1).map((p) => (
              <button
                key={p}
                onClick={() => setPage(p)}
                className={`px-4 py-2 rounded-lg ${
                  page === p
                    ? 'bg-red-600 text-white'
                    : 'bg-gray-800 text-white hover:bg-gray-700'
                }`}
              >
                {p}
              </button>
            ))}

            <button
              onClick={() => setPage(p => Math.min(totalPages, p + 1))}
              disabled={page === totalPages}
              className={`px-4 py-2 rounded-lg ${
                page === totalPages
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
