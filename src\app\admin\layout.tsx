'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { FaVideo, FaList, FaUsers, FaAd, FaChartBar, FaCog, FaSignOutAlt, FaLightbulb } from 'react-icons/fa';
import { usePathname, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();

  // Check if user is authenticated and is an admin
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login?callbackUrl=' + encodeURIComponent(pathname || '/admin'));
    } else if (status === 'authenticated') {
      if (!(session.user as any).isAdmin) {
        router.push('/');
      }
    }
  }, [status, session, router, pathname]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebarOnMobile = () => {
    if (window.innerWidth < 1024) { // lg breakpoint
      setIsSidebarOpen(false);
    }
  };

  // Close sidebar on mobile by default
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 1024) {
        setIsSidebarOpen(false);
      } else {
        setIsSidebarOpen(true);
      }
    };

    // Set initial state
    handleResize();

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  const isActive = (path: string) => {
    return pathname === path || pathname?.startsWith(path + '/');
  };

  // Show loading state while checking authentication
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p className="text-white">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // If not admin, don't render anything (should be redirected by useEffect)
  if (status === 'authenticated' && !(session.user as any).isAdmin) {
    return null;
  }

  return (
    <div className="flex flex-col min-h-screen bg-gray-900">
      <div className="flex flex-1">
        {/* Mobile overlay */}
        {isSidebarOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsSidebarOpen(false)}
          />
        )}

        {/* Sidebar - Responsive with mobile overlay */}
        <div className={`
          bg-gray-800 transition-all duration-300 ease-in-out flex flex-col
          ${isSidebarOpen ? 'w-64' : 'w-0 lg:w-20'}
          fixed lg:relative inset-y-0 left-0 z-50 lg:z-auto
          ${isSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
          overflow-hidden
        `}>
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-700 flex-shrink-0">
            {isSidebarOpen ? (
              <h1 className="text-xl font-bold text-white">Admin Panel</h1>
            ) : (
              <h1 className="text-xl font-bold text-white">AP</h1>
            )}
            <button
              onClick={toggleSidebar}
              className="text-gray-400 hover:text-white"
            >
              {isSidebarOpen ? (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                </svg>
              )}
            </button>
          </div>
          <nav className="mt-5 px-2 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
            <Link href="/admin" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin') && !isActive('/admin/videos') && !isActive('/admin/categories') && !isActive('/admin/users') && !isActive('/admin/ads') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaChartBar className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Dashboard</span>}
            </Link>
            <Link href="/admin/videos" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/videos') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaVideo className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Videos</span>}
            </Link>
            <Link href="/admin/videos/embed" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/videos/embed') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaVideo className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Embed Video</span>}
            </Link>
            <Link href="/admin/categories" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/categories') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaList className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Categories</span>}
            </Link>
            <Link href="/admin/users" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/users') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaUsers className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Users</span>}
            </Link>
            <Link href="/admin/ads" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/ads') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaAd className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Advertisements</span>}
            </Link>
            <Link href="/admin/recommendations" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/recommendations') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaLightbulb className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Recommendations</span>}
            </Link>
            <Link href="/admin/settings" onClick={closeSidebarOnMobile} className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/settings') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaCog className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Settings</span>}
            </Link>
            <div className="border-t border-gray-700 my-4"></div>
            <Link href="/" onClick={closeSidebarOnMobile} className="flex items-center px-4 py-3 mb-2 rounded-lg text-gray-400 hover:bg-gray-700 hover:text-white">
              <FaSignOutAlt className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Back to Site</span>}
            </Link>
          </nav>
        </div>

        {/* Main Content - Responsive with mobile padding */}
        <div className="flex-1 flex flex-col min-h-screen lg:ml-0">
          <header className="bg-gray-800 shadow-md flex-shrink-0 sticky top-0 z-30">
            <div className="flex items-center justify-between h-16 px-4 lg:px-6">
              {/* Mobile menu button */}
              <button
                onClick={toggleSidebar}
                className="text-gray-400 hover:text-white lg:hidden"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                </svg>
              </button>

              <h2 className="text-lg lg:text-xl font-semibold text-white">Admin Dashboard</h2>

              <div className="flex items-center">
                <span className="hidden sm:block text-sm text-gray-300 mr-4">Admin User</span>
                <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center text-white">
                  A
                </div>
              </div>
            </div>
          </header>

          <main className="flex-1 p-4 lg:p-6 overflow-y-auto bg-gray-900">
            <div className="max-w-full">
              {children}
            </div>
          </main>
        </div>
      </div>
    </div>
  );
}
