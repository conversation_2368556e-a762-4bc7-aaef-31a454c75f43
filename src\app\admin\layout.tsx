'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Fa<PERSON>ideo, FaList, FaUsers, FaAd, FaChartBar, FaCog, FaSignOutAlt, FaLightbulb } from 'react-icons/fa';
import { usePathname, useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(true);
  const pathname = usePathname();
  const router = useRouter();
  const { data: session, status } = useSession();

  // Check if user is authenticated and is an admin
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login?callbackUrl=' + encodeURIComponent(pathname || '/admin'));
    } else if (status === 'authenticated') {
      if (!(session.user as any).isAdmin) {
        router.push('/');
      }
    }
  }, [status, session, router, pathname]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const isActive = (path: string) => {
    return pathname === path || pathname?.startsWith(path + '/');
  };

  // Show loading state while checking authentication
  if (status === 'loading' || status === 'unauthenticated') {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-900">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-red-500 mx-auto mb-4"></div>
          <p className="text-white">Loading admin panel...</p>
        </div>
      </div>
    );
  }

  // If not admin, don't render anything (should be redirected by useEffect)
  if (status === 'authenticated' && !(session.user as any).isAdmin) {
    return null;
  }

  return (
    <div className="flex flex-col h-screen bg-gray-900">
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar - Fixed height with internal scrolling */}
        <div className={`bg-gray-800 ${isSidebarOpen ? 'w-64' : 'w-20'} transition-all duration-300 ease-in-out flex flex-col h-full`}>
          <div className="flex items-center justify-between h-16 px-4 border-b border-gray-700 flex-shrink-0">
            {isSidebarOpen ? (
              <h1 className="text-xl font-bold text-white">Admin Panel</h1>
            ) : (
              <h1 className="text-xl font-bold text-white">AP</h1>
            )}
            <button
              onClick={toggleSidebar}
              className="text-gray-400 hover:text-white"
            >
              {isSidebarOpen ? (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 19l-7-7 7-7m8 14l-7-7 7-7" />
                </svg>
              ) : (
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 5l7 7-7 7M5 5l7 7-7 7" />
                </svg>
              )}
            </button>
          </div>
          <nav className="mt-5 px-2 flex-1 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800">
            <Link href="/admin" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin') && !isActive('/admin/videos') && !isActive('/admin/categories') && !isActive('/admin/users') && !isActive('/admin/ads') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaChartBar className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Dashboard</span>}
            </Link>
            <Link href="/admin/videos" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/videos') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaVideo className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Videos</span>}
            </Link>
            <Link href="/admin/videos/embed" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/videos/embed') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaVideo className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Embed Video</span>}
            </Link>
            <Link href="/admin/categories" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/categories') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaList className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Categories</span>}
            </Link>
            <Link href="/admin/users" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/users') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaUsers className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Users</span>}
            </Link>
            <Link href="/admin/ads" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/ads') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaAd className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Advertisements</span>}
            </Link>
            <Link href="/admin/recommendations" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/recommendations') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaLightbulb className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Recommendations</span>}
            </Link>
            <Link href="/admin/settings" className={`flex items-center px-4 py-3 mb-2 rounded-lg ${isActive('/admin/settings') ? 'bg-gray-700 text-white' : 'text-gray-400 hover:bg-gray-700 hover:text-white'}`}>
              <FaCog className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Settings</span>}
            </Link>
            <div className="border-t border-gray-700 my-4"></div>
            <Link href="/" className="flex items-center px-4 py-3 mb-2 rounded-lg text-gray-400 hover:bg-gray-700 hover:text-white">
              <FaSignOutAlt className="w-5 h-5" />
              {isSidebarOpen && <span className="ml-3">Back to Site</span>}
            </Link>
          </nav>
        </div>

        {/* Main Content - Fixed header with scrollable content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <header className="bg-gray-800 shadow-md flex-shrink-0">
            <div className="flex items-center justify-between h-16 px-6">
              <h2 className="text-xl font-semibold text-white">Admin Dashboard</h2>
              <div className="flex items-center">
                <span className="text-sm text-gray-300 mr-4">Admin User</span>
                <div className="w-8 h-8 rounded-full bg-gray-600 flex items-center justify-center text-white">
                  A
                </div>
              </div>
            </div>
          </header>
          <main className="flex-1 p-6 overflow-y-auto">
            {children}
          </main>
        </div>
      </div>
    </div>
  );
}
