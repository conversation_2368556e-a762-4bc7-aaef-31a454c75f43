'use client';

import { useState, useEffect } from 'react';
import { <PERSON>aF<PERSON>, FaChartLine, FaEye, FaClock, FaCalendarAlt } from 'react-icons/fa';
import VideoCard from '@/app/components/VideoCard';
import axios from 'axios';

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl?: string;
  embedUrl?: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  tags: string[];
  uploadedBy: string;
  isPublished: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

export default function TrendingPage() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [timeFilter, setTimeFilter] = useState('week'); // today, week, month, all
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchTrendingVideos = async (pageNum = 1, timeRange = 'week') => {
    try {
      setLoading(true);
      const response = await axios.get(`/api/videos/trending?page=${pageNum}&limit=20&timeRange=${timeRange}`);
      
      if (response.data && response.data.videos) {
        if (pageNum === 1) {
          setVideos(response.data.videos);
        } else {
          setVideos(prev => [...prev, ...response.data.videos]);
        }
        setHasMore(response.data.hasMore || false);
      }
    } catch (error) {
      console.error('Error fetching trending videos:', error);
      setError('Failed to load trending videos');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTrendingVideos(1, timeFilter);
    setPage(1);
  }, [timeFilter]);

  const loadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchTrendingVideos(nextPage, timeFilter);
    }
  };

  const timeFilterOptions = [
    { value: 'today', label: 'Today', icon: FaClock },
    { value: 'week', label: 'This Week', icon: FaCalendarAlt },
    { value: 'month', label: 'This Month', icon: FaCalendarAlt },
    { value: 'all', label: 'All Time', icon: FaChartLine }
  ];

  return (
    <div className="min-h-screen bg-ph-black-950">
      <div className="container-ph py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <FaFire className="text-ph-orange-500 text-3xl mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold text-white">Trending Videos</h1>
          </div>
          <p className="text-ph-gray-400 text-lg">
            Discover the hottest videos that are trending right now
          </p>
        </div>

        {/* Time Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-3">
            {timeFilterOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <button
                  key={option.value}
                  onClick={() => setTimeFilter(option.value)}
                  className={`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                    timeFilter === option.value
                      ? 'bg-ph-orange-500 text-white'
                      : 'bg-ph-gray-800 text-ph-gray-300 hover:bg-ph-gray-700 hover:text-white'
                  }`}
                >
                  <IconComponent className="mr-2" />
                  {option.label}
                </button>
              );
            })}
          </div>
        </div>

        {/* Loading State */}
        {loading && page === 1 && (
          <div className="flex items-center justify-center py-12">
            <div className="text-ph-gray-400 text-xl">Loading trending videos...</div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex items-center justify-center py-12">
            <div className="text-red-500 text-xl">{error}</div>
          </div>
        )}

        {/* Videos Grid */}
        {!loading && !error && videos.length > 0 && (
          <>
            <div className="grid-ph-videos mb-8">
              {videos.map((video, index) => (
                <VideoCard
                  key={`${video._id}-${index}`}
                  video={video}
                  showRank={true}
                  rank={index + 1}
                />
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="flex justify-center">
                <button
                  onClick={loadMore}
                  disabled={loading}
                  className="btn-ph-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Loading...' : 'Load More Videos'}
                </button>
              </div>
            )}
          </>
        )}

        {/* No Videos State */}
        {!loading && !error && videos.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12">
            <FaFire className="text-ph-gray-600 text-6xl mb-4" />
            <h2 className="text-2xl font-bold text-ph-gray-400 mb-2">No Trending Videos</h2>
            <p className="text-ph-gray-500 text-center max-w-md">
              There are no trending videos for the selected time period. Check back later or try a different time range.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
