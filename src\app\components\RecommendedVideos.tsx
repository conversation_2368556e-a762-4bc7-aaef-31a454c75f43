'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import VideoCard from './VideoCard';
import { FaSpinner } from 'react-icons/fa';
import axios from 'axios';

interface Video {
  _id: string;
  title: string;
  thumbnailUrl: string;
  duration: number;
  views: number;
  likes: number;
  slug: string;
  recommendationScore?: number;
}

interface RecommendedVideosProps {
  excludeVideoId?: string;
  limit?: number;
  title?: string;
}

const RecommendedVideos: React.FC<RecommendedVideosProps> = ({
  excludeVideoId,
  limit = 6,
  title = 'Recommended for You'
}) => {
  const { data: session } = useSession();
  const [videos, setVideos] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [recommendationType, setRecommendationType] = useState<'personalized' | 'trending'>('trending');

  useEffect(() => {
    const fetchRecommendations = async () => {
      try {
        setLoading(true);
        
        // Build query parameters
        const params = new URLSearchParams();
        params.append('limit', limit.toString());
        if (excludeVideoId) {
          params.append('exclude', excludeVideoId);
        }
        
        const response = await axios.get(`/api/recommendations?${params.toString()}`);
        
        if (response.data) {
          setVideos(response.data.recommendations || []);
          setRecommendationType(response.data.type || 'trending');
        }
      } catch (error) {
        console.error('Error fetching recommendations:', error);
        setError('Failed to load recommendations');
      } finally {
        setLoading(false);
      }
    };
    
    fetchRecommendations();
  }, [excludeVideoId, limit, session]);
  
  // Adjust title based on recommendation type
  const displayTitle = recommendationType === 'personalized' ? title : 'Trending Videos';

  if (loading) {
    return (
      <div className="w-full py-8 flex justify-center">
        <FaSpinner className="animate-spin text-red-500 text-2xl" />
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900/30 text-red-200 p-4 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  if (videos.length === 0) {
    return null; // Don't show anything if no recommendations
  }

  return (
    <div className="w-full">
      <h2 className="text-xl font-bold text-white mb-4">{displayTitle}</h2>
      
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {videos.map((video) => (
          <VideoCard
            key={video._id}
            id={video._id}
            title={video.title}
            thumbnail={video.thumbnailUrl}
            duration={video.duration}
            views={video.views}
            likes={video.likes}
            slug={video.slug}
          />
        ))}
      </div>
    </div>
  );
};

export default RecommendedVideos;
