import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/app/api/auth/[...nextauth]/route';
import dbConnect from '@/app/utils/dbConnect';
import Comment from '@/app/models/Comment';

export async function PUT(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const id = params.id;
    const { content } = await req.json();

    // Get user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to update a comment' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return NextResponse.json(
        { error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Find user
    const user = await import('@/app/models/User').then(module =>
      module.default.findOne({ email: session.user.email })
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user is the comment owner or an admin
    if (comment.user.toString() !== user._id.toString() && !user.isAdmin) {
      return NextResponse.json(
        { error: 'You are not authorized to update this comment' },
        { status: 403 }
      );
    }

    // Update comment
    comment.content = content;
    await comment.save();

    // Populate user data
    await comment.populate('user', 'username profilePicture');

    return NextResponse.json(comment);
  } catch (error) {
    console.error('Error updating comment:', error);
    return NextResponse.json(
      { error: 'Failed to update comment' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const id = params.id;

    // Get user session
    const session = await getServerSession(authOptions);

    if (!session || !session.user) {
      return NextResponse.json(
        { error: 'You must be logged in to delete a comment' },
        { status: 401 }
      );
    }

    // Connect to the database
    await dbConnect();

    // Find comment
    const comment = await Comment.findById(id);

    if (!comment) {
      return NextResponse.json(
        { error: 'Comment not found' },
        { status: 404 }
      );
    }

    // Find user
    const user = await import('@/app/models/User').then(module =>
      module.default.findOne({ email: session.user.email })
    );

    if (!user) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      );
    }

    // Check if user is the comment owner or an admin
    if (comment.user.toString() !== user._id.toString() && !user.isAdmin) {
      return NextResponse.json(
        { error: 'You are not authorized to delete this comment' },
        { status: 403 }
      );
    }

    // Delete comment (soft delete)
    comment.isActive = false;
    await comment.save();

    return NextResponse.json({ message: 'Comment deleted successfully' });
  } catch (error) {
    console.error('Error deleting comment:', error);
    return NextResponse.json(
      { error: 'Failed to delete comment' },
      { status: 500 }
    );
  }
}
