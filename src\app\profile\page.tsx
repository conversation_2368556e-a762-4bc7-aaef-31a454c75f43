'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { FaUser, FaEdit, FaEye, FaHeart, FaHistory, FaSignOutAlt } from 'react-icons/fa';
import Link from 'next/link';
import VideoCard from '@/app/components/VideoCard';

interface UserProfile {
  _id: string;
  username: string;
  email: string;
  profilePicture?: string;
  isAdmin: boolean;
  createdAt: string;
}

interface Video {
  _id: string;
  title: string;
  thumbnailUrl: string;
  duration: number;
  views: number;
  likes: number;
  slug: string;
}

export default function ProfilePage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('favorites');
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [favorites, setFavorites] = useState<Video[]>([]);
  const [watchHistory, setWatchHistory] = useState<Video[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  // Redirect if not logged in
  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/login');
    }
  }, [status, router]);

  // Fetch user profile data
  useEffect(() => {
    const fetchProfileData = async () => {
      if (status !== 'authenticated') return;

      try {
        setLoading(true);

        // Fetch user profile
        const profileResponse = await fetch('/api/profile');
        const profileData = await profileResponse.json();

        if (!profileResponse.ok) {
          throw new Error(profileData.error || 'Failed to fetch profile');
        }

        setProfile(profileData);

        // Fetch favorites
        const favoritesResponse = await fetch('/api/profile/favorites');
        const favoritesData = await favoritesResponse.json();

        if (!favoritesResponse.ok) {
          throw new Error(favoritesData.error || 'Failed to fetch favorites');
        }

        setFavorites(favoritesData.favorites || []);

        // Fetch watch history
        const historyResponse = await fetch('/api/profile/history');
        const historyData = await historyResponse.json();

        if (!historyResponse.ok) {
          throw new Error(historyData.error || 'Failed to fetch watch history');
        }

        setWatchHistory(historyData.videos || []);
      } catch (error: any) {
        setError(error.message || 'An error occurred while fetching profile data');
        console.error('Error fetching profile data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProfileData();
  }, [status]);

  // Handle logout
  const handleLogout = async () => {
    if (confirm('Are you sure you want to log out?')) {
      router.push('/api/auth/signout');
    }
  };

  if (status === 'loading' || loading) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Loading profile...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-900 text-red-200 p-4 rounded-lg">
        <p>{error}</p>
      </div>
    );
  }

  if (!session || !profile) {
    return (
      <div className="text-center py-12">
        <p className="text-gray-400">Please log in to view your profile</p>
        <Link href="/auth/login" className="btn-primary mt-4 inline-block">
          Login
        </Link>
      </div>
    );
  }

  return (
    <div>
      {/* Profile Header */}
      <div className="bg-gray-800 rounded-lg p-6 mb-8">
        <div className="flex flex-col md:flex-row items-center gap-6">
          <div className="w-24 h-24 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0">
            {profile.profilePicture ? (
              <img
                src={profile.profilePicture}
                alt={profile.username}
                className="w-full h-full rounded-full object-cover"
              />
            ) : (
              <FaUser className="text-gray-400 text-4xl" />
            )}
          </div>

          <div className="flex-grow text-center md:text-left">
            <h1 className="text-2xl font-bold text-white">{profile.username}</h1>
            <p className="text-gray-400">{profile.email}</p>
            <p className="text-gray-400 text-sm mt-1">
              Member since {new Date(profile.createdAt).toLocaleDateString()}
            </p>

            <div className="mt-4 flex flex-wrap gap-3 justify-center md:justify-start">
              <Link href="/profile/edit" className="btn-secondary flex items-center">
                <FaEdit className="mr-2" /> Edit Profile
              </Link>

              {profile.isAdmin && (
                <Link href="/admin" className="btn-secondary flex items-center">
                  <FaUser className="mr-2" /> Admin Panel
                </Link>
              )}

              <button
                onClick={handleLogout}
                className="btn-secondary flex items-center"
              >
                <FaSignOutAlt className="mr-2" /> Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-700 mb-6">
        <div className="flex overflow-x-auto">
          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'favorites'
                ? 'text-red-500 border-b-2 border-red-500'
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('favorites')}
          >
            <FaHeart className="inline mr-2" /> Favorites
          </button>

          <button
            className={`px-4 py-2 font-medium ${
              activeTab === 'history'
                ? 'text-red-500 border-b-2 border-red-500'
                : 'text-gray-400 hover:text-white'
            }`}
            onClick={() => setActiveTab('history')}
          >
            <FaHistory className="inline mr-2" /> Watch History
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'favorites' && (
        <div>
          <h2 className="text-xl font-bold text-white mb-4">Your Favorites</h2>

          {favorites.length === 0 ? (
            <div className="text-center py-8 bg-gray-800 rounded-lg">
              <p className="text-gray-400 mb-4">You haven't added any videos to your favorites yet</p>
              <Link href="/" className="btn-primary">
                Browse Videos
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {favorites.map((video) => (
                <VideoCard
                  key={video._id}
                  id={video._id}
                  title={video.title}
                  thumbnail={video.thumbnailUrl}
                  duration={video.duration}
                  views={video.views}
                  likes={video.likes}
                  slug={video.slug}
                />
              ))}
            </div>
          )}
        </div>
      )}

      {activeTab === 'history' && (
        <div>
          <h2 className="text-xl font-bold text-white mb-4">Watch History</h2>

          {watchHistory.length === 0 ? (
            <div className="text-center py-8 bg-gray-800 rounded-lg">
              <p className="text-gray-400 mb-4">Your watch history is empty</p>
              <Link href="/" className="btn-primary">
                Browse Videos
              </Link>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              {watchHistory.map((video) => (
                <VideoCard
                  key={video._id}
                  id={video._id}
                  title={video.title}
                  thumbnail={video.thumbnailUrl}
                  duration={video.duration}
                  views={video.views}
                  likes={video.likes}
                  slug={video.slug}
                />
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
