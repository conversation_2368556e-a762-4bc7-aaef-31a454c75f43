import mongoose, { Schema, Document } from 'mongoose';

export interface IVideo extends Document {
  title: string;
  slug: string;
  description: string;
  thumbnail: string;
  embedUrl: string;
  videoUrl?: string;
  sourceUrl?: string;
  sourceSite: string;
  duration: number; // in seconds
  views: number;
  likes: number;
  dislikes: number;
  categories: mongoose.Types.ObjectId[];
  tags: string[];
  uploadedBy?: mongoose.Types.ObjectId;
  isActive: boolean;
  isPublished: boolean;
  isVerified: boolean; // for age verification
  createdAt: Date;
  updatedAt: Date;
}

const VideoSchema: Schema = new Schema(
  {
    title: {
      type: String,
      required: [true, 'Title is required'],
      trim: true,
      maxlength: [100, 'Title cannot be more than 100 characters'],
    },
    slug: {
      type: String,
      required: true,
      unique: true,
      lowercase: true,
      trim: true,
    },
    description: {
      type: String,
      default: '',
      trim: true,
      maxlength: [5000, 'Description cannot be more than 5000 characters'],
    },
    thumbnail: {
      type: String,
      required: [true, 'Thumbnail is required'],
    },
    embedUrl: {
      type: String,
      required: [true, 'Embed URL is required'],
    },
    videoUrl: {
      type: String,
    },
    sourceUrl: {
      type: String,
    },
    sourceSite: {
      type: String,
      required: [true, 'Source site is required'],
    },
    duration: {
      type: Number,
      default: 0,
      min: [1, 'Duration must be at least 1 second'],
    },
    views: {
      type: Number,
      default: 0,
    },
    likes: {
      type: Number,
      default: 0,
    },
    dislikes: {
      type: Number,
      default: 0,
    },
    categories: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Category',
      required: [true, 'At least one category is required'],
    }],
    tags: [{
      type: String,
      trim: true,
    }],
    uploadedBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    isActive: {
      type: Boolean,
      default: true,
    },
    isPublished: {
      type: Boolean,
      default: false,
    },
    isVerified: {
      type: Boolean,
      default: false,
    },
  },
  { timestamps: true }
);

// Create text index for search
VideoSchema.index({ title: 'text', description: 'text', tags: 'text' });

// Create slug from title before saving
VideoSchema.pre('save', function(next) {
  if (this.isModified('title') || !this.slug) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .concat('-', Date.now().toString().slice(-4));
  }
  next();
});

export default mongoose.models.Video || mongoose.model<IVideo>('Video', VideoSchema);
