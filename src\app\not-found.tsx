import Link from 'next/link';
import { FaH<PERSON>, FaSearch, FaExclamationTriangle } from 'react-icons/fa';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-ph-black-950 flex items-center justify-center">
      <div className="container-ph">
        <div className="text-center">
          {/* 404 Icon */}
          <div className="mb-8">
            <FaExclamationTriangle className="text-ph-orange-500 text-8xl mx-auto mb-4" />
            <h1 className="text-6xl md:text-8xl font-bold text-white mb-4">404</h1>
            <h2 className="text-2xl md:text-3xl font-semibold text-ph-gray-300 mb-6">
              Page Not Found
            </h2>
          </div>

          {/* Error Message */}
          <div className="mb-8">
            <p className="text-lg text-ph-gray-400 mb-4 max-w-2xl mx-auto">
              Oops! The page you're looking for doesn't exist. It might have been moved, deleted, or you entered the wrong URL.
            </p>
            <p className="text-ph-gray-500 max-w-xl mx-auto">
              Don't worry, you can find plenty of other content on our homepage or use the search feature to find what you're looking for.
            </p>
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
            <Link
              href="/"
              className="btn-ph-primary flex items-center"
            >
              <FaHome className="mr-2" />
              Go to Homepage
            </Link>
            
            <Link
              href="/search"
              className="btn-ph-secondary flex items-center"
            >
              <FaSearch className="mr-2" />
              Search Videos
            </Link>
          </div>

          {/* Popular Links */}
          <div className="mt-12">
            <h3 className="text-lg font-semibold text-white mb-4">
              Popular Pages
            </h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/categories"
                className="text-ph-orange-500 hover:text-ph-orange-400 transition-colors duration-200"
              >
                Categories
              </Link>
              <Link
                href="/trending"
                className="text-ph-orange-500 hover:text-ph-orange-400 transition-colors duration-200"
              >
                Trending
              </Link>
              <Link
                href="/newest"
                className="text-ph-orange-500 hover:text-ph-orange-400 transition-colors duration-200"
              >
                Newest
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
