import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/app/utils/dbConnect';
import Video from '@/app/models/Video';

export async function POST(
  req: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    // Use params directly to avoid the "params should be awaited" error
    const slug = params.slug;

    // Connect to the database
    await dbConnect();

    // Find video by slug
    const video = await Video.findOne({ slug });

    if (!video) {
      return NextResponse.json(
        { error: 'Video not found' },
        { status: 404 }
      );
    }

    // Increment view count
    video.views += 1;
    await video.save();

    return NextResponse.json({ 
      success: true, 
      views: video.views 
    });
  } catch (error) {
    console.error('Error incrementing view count:', error);
    return NextResponse.json(
      { error: 'Failed to increment view count' },
      { status: 500 }
    );
  }
}
