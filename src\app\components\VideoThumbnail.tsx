'use client';

import { FaPlay, FaClock } from 'react-icons/fa';
import { formatDuration } from '@/app/utils/formatters';

interface VideoThumbnailProps {
  title: string;
  category?: string;
  duration?: number;
}

export default function VideoThumbnail({ title, category, duration }: VideoThumbnailProps) {
  // Generate a random background color from a set of dark colors
  const getRandomColor = () => {
    const colors = [
      'bg-gray-800',
      'bg-gray-900',
      'bg-slate-800',
      'bg-slate-900',
      'bg-zinc-800',
      'bg-zinc-900',
      'bg-neutral-800',
      'bg-neutral-900',
    ];
    return colors[Math.floor(Math.random() * colors.length)];
  };

  // Generate a random gradient for more visual interest
  const getRandomGradient = () => {
    const gradients = [
      'bg-gradient-to-r from-gray-900 to-gray-800',
      'bg-gradient-to-r from-slate-900 to-slate-800',
      'bg-gradient-to-r from-zinc-900 to-zinc-800',
      'bg-gradient-to-r from-neutral-900 to-neutral-800',
      'bg-gradient-to-br from-gray-900 to-gray-800',
      'bg-gradient-to-br from-slate-900 to-slate-800',
      'bg-gradient-to-br from-zinc-900 to-zinc-800',
      'bg-gradient-to-br from-neutral-900 to-neutral-800',
    ];
    return gradients[Math.floor(Math.random() * gradients.length)];
  };

  return (
    <div className={`relative aspect-video overflow-hidden rounded-lg ${getRandomGradient()}`}>
      {/* Thumbnail content */}
      <div className="absolute inset-0 flex flex-col items-center justify-center p-4 text-center">
        <div className="w-12 h-12 rounded-full bg-red-600/80 flex items-center justify-center mb-3">
          <FaPlay className="text-white ml-1" />
        </div>
        <p className="text-white text-sm font-medium line-clamp-2">{title}</p>
        {category && (
          <span className="mt-2 px-2 py-1 bg-red-600/80 text-white text-xs rounded-full">
            {category}
          </span>
        )}
      </div>

      {/* Duration badge */}
      {duration && (
        <div className="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
          <FaClock className="inline mr-1" />
          {formatDuration(duration)}
        </div>
      )}
    </div>
  );
}
