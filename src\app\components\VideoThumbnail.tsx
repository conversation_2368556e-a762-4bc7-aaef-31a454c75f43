'use client';

import { FaPlay, FaClock } from 'react-icons/fa';
import { formatDuration } from '@/app/utils/formatters';

interface VideoThumbnailProps {
  title: string;
  category?: string;
  duration?: number;
  videoUrl?: string;
}

export default function VideoThumbnail({ title, category, duration, videoUrl }: VideoThumbnailProps) {
  // Generate a random gradient with purple/blue theme
  const getRandomGradient = () => {
    const gradients = [
      'bg-gradient-to-r from-purple-900 to-blue-900',
      'bg-gradient-to-r from-indigo-900 to-purple-900',
      'bg-gradient-to-r from-blue-900 to-indigo-900',
      'bg-gradient-to-br from-purple-900 to-blue-800',
      'bg-gradient-to-br from-indigo-900 to-purple-800',
      'bg-gradient-to-br from-blue-900 to-purple-800',
      'bg-gradient-to-tl from-purple-800 to-blue-900',
      'bg-gradient-to-tl from-indigo-800 to-purple-900',
    ];
    return gradients[Math.floor(Math.random() * gradients.length)];
  };

  // Generate thumbnail from video if URL is provided
  const generateVideoThumbnail = (videoUrl: string) => {
    // For embedded videos, try to extract thumbnail
    if (videoUrl.includes('xhamster.com')) {
      // Extract video ID and generate thumbnail URL
      const videoId = videoUrl.split('/').pop()?.split('-').pop();
      if (videoId) {
        return `https://thumb-p${Math.floor(Math.random() * 26) + 1}.xhcdn.com/a/${videoId}/000/001.jpg`;
      }
    }
    return null;
  };

  const thumbnailUrl = videoUrl ? generateVideoThumbnail(videoUrl) : null;

  return (
    <div className={`relative aspect-video overflow-hidden rounded-lg ${thumbnailUrl ? 'bg-gray-900' : getRandomGradient()}`}>
      {/* Show actual thumbnail if available */}
      {thumbnailUrl ? (
        <img
          src={thumbnailUrl}
          alt={title}
          className="w-full h-full object-cover"
          loading="lazy"
          decoding="async"
          sizes="(max-width: 640px) 160px, (max-width: 768px) 280px, (max-width: 1024px) 320px, 400px"
          onError={(e) => {
            // Fallback to gradient background if image fails to load
            e.currentTarget.style.display = 'none';
          }}
        />
      ) : (
        /* Fallback thumbnail content */
        <div className="absolute inset-0 flex flex-col items-center justify-center p-4 text-center">
          <div className="w-12 h-12 rounded-full bg-purple-600/80 flex items-center justify-center mb-3 shadow-lg">
            <FaPlay className="text-white ml-1" />
          </div>
          <p className="text-white text-sm font-medium line-clamp-2">{title}</p>
          {category && (
            <span className="mt-2 px-2 py-1 bg-purple-600/80 text-white text-xs rounded-full">
              {category}
            </span>
          )}
        </div>
      )}

      {/* Duration badge */}
      {duration && (
        <div className="absolute bottom-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded font-semibold">
          {formatDuration(duration)}
        </div>
      )}
    </div>
  );
}
