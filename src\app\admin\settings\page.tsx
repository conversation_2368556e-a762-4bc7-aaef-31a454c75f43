'use client';

import { useState } from 'react';
import { FaSave, FaServer, FaEnvelope, FaGlobe, FaDatabase, FaLock } from 'react-icons/fa';

interface Settings {
  site: {
    name: string;
    description: string;
    url: string;
    logo: string;
    favicon: string;
  };
  security: {
    enableRegistration: boolean;
    requireEmailVerification: boolean;
    enableTwoFactor: boolean;
    maxLoginAttempts: number;
    sessionTimeout: number;
  };
  content: {
    enableComments: boolean;
    moderateComments: boolean;
    enableRatings: boolean;
    enableRecommendations: boolean;
    maxVideoSize: number;
    allowedVideoFormats: string[];
  };
  email: {
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
    fromEmail: string;
    fromName: string;
  };
}

export default function SettingsAdmin() {
  const [settings, setSettings] = useState<Settings>({
    site: {
      name: 'Adult Video Platform',
      description: 'Premium adult entertainment platform',
      url: 'https://yoursite.com',
      logo: '/logo.png',
      favicon: '/favicon.ico'
    },
    security: {
      enableRegistration: true,
      requireEmailVerification: true,
      enableTwoFactor: false,
      maxLoginAttempts: 5,
      sessionTimeout: 24
    },
    content: {
      enableComments: true,
      moderateComments: true,
      enableRatings: true,
      enableRecommendations: true,
      maxVideoSize: 500,
      allowedVideoFormats: ['mp4', 'webm', 'avi']
    },
    email: {
      smtpHost: 'smtp.gmail.com',
      smtpPort: 587,
      smtpUser: '',
      smtpPassword: '',
      fromEmail: '<EMAIL>',
      fromName: 'Adult Video Platform'
    }
  });

  const [activeTab, setActiveTab] = useState('site');
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    setSaving(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      alert('Settings saved successfully!');
    } catch (error) {
      alert('Error saving settings');
    } finally {
      setSaving(false);
    }
  };

  const updateSetting = (section: keyof Settings, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [key]: value
      }
    }));
  };

  const tabs = [
    { id: 'site', label: 'Site Settings', icon: FaGlobe },
    { id: 'security', label: 'Security', icon: FaLock },
    { id: 'content', label: 'Content', icon: FaServer },
    { id: 'email', label: 'Email', icon: FaEnvelope }
  ];

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">System Settings</h1>
        <button
          onClick={handleSave}
          disabled={saving}
          className="btn-primary flex items-center"
        >
          <FaSave className="mr-2" />
          {saving ? 'Saving...' : 'Save Settings'}
        </button>
      </div>

      <div className="bg-gray-800 rounded-lg overflow-hidden">
        {/* Tabs */}
        <div className="flex border-b border-gray-700">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id)}
              className={`flex items-center px-6 py-4 text-sm font-medium ${
                activeTab === tab.id
                  ? 'text-red-500 border-b-2 border-red-500 bg-gray-700'
                  : 'text-gray-400 hover:text-white hover:bg-gray-700'
              }`}
            >
              <tab.icon className="mr-2" />
              {tab.label}
            </button>
          ))}
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {activeTab === 'site' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white mb-4">Site Configuration</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-white mb-2">Site Name</label>
                  <input
                    type="text"
                    value={settings.site.name}
                    onChange={(e) => updateSetting('site', 'name', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">Site URL</label>
                  <input
                    type="url"
                    value={settings.site.url}
                    onChange={(e) => updateSetting('site', 'url', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="block text-white mb-2">Site Description</label>
                  <textarea
                    value={settings.site.description}
                    onChange={(e) => updateSetting('site', 'description', e.target.value)}
                    className="input-field w-full h-24"
                    rows={3}
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">Logo URL</label>
                  <input
                    type="text"
                    value={settings.site.logo}
                    onChange={(e) => updateSetting('site', 'logo', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">Favicon URL</label>
                  <input
                    type="text"
                    value={settings.site.favicon}
                    onChange={(e) => updateSetting('site', 'favicon', e.target.value)}
                    className="input-field w-full"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'security' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white mb-4">Security Settings</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.enableRegistration}
                      onChange={(e) => updateSetting('security', 'enableRegistration', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Enable User Registration</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.requireEmailVerification}
                      onChange={(e) => updateSetting('security', 'requireEmailVerification', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Require Email Verification</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.security.enableTwoFactor}
                      onChange={(e) => updateSetting('security', 'enableTwoFactor', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Enable Two-Factor Authentication</span>
                  </label>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-white mb-2">Max Login Attempts</label>
                    <input
                      type="number"
                      value={settings.security.maxLoginAttempts}
                      onChange={(e) => updateSetting('security', 'maxLoginAttempts', parseInt(e.target.value))}
                      className="input-field w-full"
                      min="1"
                      max="10"
                    />
                  </div>

                  <div>
                    <label className="block text-white mb-2">Session Timeout (hours)</label>
                    <input
                      type="number"
                      value={settings.security.sessionTimeout}
                      onChange={(e) => updateSetting('security', 'sessionTimeout', parseInt(e.target.value))}
                      className="input-field w-full"
                      min="1"
                      max="168"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'content' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white mb-4">Content Settings</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.content.enableComments}
                      onChange={(e) => updateSetting('content', 'enableComments', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Enable Comments</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.content.moderateComments}
                      onChange={(e) => updateSetting('content', 'moderateComments', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Moderate Comments</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.content.enableRatings}
                      onChange={(e) => updateSetting('content', 'enableRatings', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Enable Video Ratings</span>
                  </label>

                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={settings.content.enableRecommendations}
                      onChange={(e) => updateSetting('content', 'enableRecommendations', e.target.checked)}
                      className="mr-3"
                    />
                    <span className="text-white">Enable Recommendations</span>
                  </label>
                </div>

                <div className="space-y-4">
                  <div>
                    <label className="block text-white mb-2">Max Video Size (MB)</label>
                    <input
                      type="number"
                      value={settings.content.maxVideoSize}
                      onChange={(e) => updateSetting('content', 'maxVideoSize', parseInt(e.target.value))}
                      className="input-field w-full"
                      min="1"
                      max="2000"
                    />
                  </div>

                  <div>
                    <label className="block text-white mb-2">Allowed Video Formats</label>
                    <div className="space-y-2">
                      {['mp4', 'webm', 'avi', 'mov', 'wmv'].map((format) => (
                        <label key={format} className="flex items-center">
                          <input
                            type="checkbox"
                            checked={settings.content.allowedVideoFormats.includes(format)}
                            onChange={(e) => {
                              const formats = settings.content.allowedVideoFormats;
                              if (e.target.checked) {
                                updateSetting('content', 'allowedVideoFormats', [...formats, format]);
                              } else {
                                updateSetting('content', 'allowedVideoFormats', formats.filter(f => f !== format));
                              }
                            }}
                            className="mr-2"
                          />
                          <span className="text-white">.{format}</span>
                        </label>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'email' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-white mb-4">Email Configuration</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-white mb-2">SMTP Host</label>
                  <input
                    type="text"
                    value={settings.email.smtpHost}
                    onChange={(e) => updateSetting('email', 'smtpHost', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">SMTP Port</label>
                  <input
                    type="number"
                    value={settings.email.smtpPort}
                    onChange={(e) => updateSetting('email', 'smtpPort', parseInt(e.target.value))}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">SMTP Username</label>
                  <input
                    type="text"
                    value={settings.email.smtpUser}
                    onChange={(e) => updateSetting('email', 'smtpUser', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">SMTP Password</label>
                  <input
                    type="password"
                    value={settings.email.smtpPassword}
                    onChange={(e) => updateSetting('email', 'smtpPassword', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">From Email</label>
                  <input
                    type="email"
                    value={settings.email.fromEmail}
                    onChange={(e) => updateSetting('email', 'fromEmail', e.target.value)}
                    className="input-field w-full"
                  />
                </div>

                <div>
                  <label className="block text-white mb-2">From Name</label>
                  <input
                    type="text"
                    value={settings.email.fromName}
                    onChange={(e) => updateSetting('email', 'fromName', e.target.value)}
                    className="input-field w-full"
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
