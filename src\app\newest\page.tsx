'use client';

import { useState, useEffect } from 'react';
import { <PERSON>a<PERSON>lock, FaCalendarAlt, FaE<PERSON>, FaFilter } from 'react-icons/fa';
import VideoCard from '@/app/components/VideoCard';
import axios from 'axios';

interface Video {
  _id: string;
  title: string;
  description: string;
  slug: string;
  duration: number;
  views: number;
  likes: number;
  dislikes: number;
  thumbnailUrl: string;
  videoUrl?: string;
  embedUrl?: string;
  sourceUrl?: string;
  sourceSite?: string;
  categories: Array<{
    _id: string;
    name: string;
    slug: string;
  }>;
  tags: string[];
  uploadedBy: string;
  isPublished: boolean;
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface Category {
  _id: string;
  name: string;
  slug: string;
}

export default function NewestPage() {
  const [videos, setVideos] = useState<Video[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('');
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);

  const fetchNewestVideos = async (pageNum = 1, categoryId = '') => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: pageNum.toString(),
        limit: '20',
        sort: 'newest'
      });
      
      if (categoryId) {
        params.append('category', categoryId);
      }

      const response = await axios.get(`/api/videos?${params.toString()}`);
      
      if (response.data && response.data.videos) {
        if (pageNum === 1) {
          setVideos(response.data.videos);
        } else {
          setVideos(prev => [...prev, ...response.data.videos]);
        }
        setHasMore(response.data.hasMore || false);
      }
    } catch (error) {
      console.error('Error fetching newest videos:', error);
      setError('Failed to load newest videos');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await axios.get('/api/categories');
      if (response.data && response.data.categories) {
        setCategories(response.data.categories);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  useEffect(() => {
    fetchCategories();
  }, []);

  useEffect(() => {
    fetchNewestVideos(1, selectedCategory);
    setPage(1);
  }, [selectedCategory]);

  const loadMore = () => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      fetchNewestVideos(nextPage, selectedCategory);
    }
  };

  return (
    <div className="min-h-screen bg-ph-black-950">
      <div className="container-ph py-6">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <FaClock className="text-ph-orange-500 text-3xl mr-3" />
            <h1 className="text-3xl md:text-4xl font-bold text-white">Newest Videos</h1>
          </div>
          <p className="text-ph-gray-400 text-lg">
            Check out the latest videos uploaded to our platform
          </p>
        </div>

        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <FaFilter className="text-ph-orange-500 mr-2" />
            <h2 className="text-lg font-semibold text-white">Filter by Category</h2>
          </div>
          <div className="flex flex-wrap gap-3">
            <button
              onClick={() => setSelectedCategory('')}
              className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                selectedCategory === ''
                  ? 'bg-ph-orange-500 text-white'
                  : 'bg-ph-gray-800 text-ph-gray-300 hover:bg-ph-gray-700 hover:text-white'
              }`}
            >
              All Categories
            </button>
            {categories.map((category) => (
              <button
                key={category._id}
                onClick={() => setSelectedCategory(category._id)}
                className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
                  selectedCategory === category._id
                    ? 'bg-ph-orange-500 text-white'
                    : 'bg-ph-gray-800 text-ph-gray-300 hover:bg-ph-gray-700 hover:text-white'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Loading State */}
        {loading && page === 1 && (
          <div className="flex items-center justify-center py-12">
            <div className="text-ph-gray-400 text-xl">Loading newest videos...</div>
          </div>
        )}

        {/* Error State */}
        {error && (
          <div className="flex items-center justify-center py-12">
            <div className="text-red-500 text-xl">{error}</div>
          </div>
        )}

        {/* Videos Grid */}
        {!loading && !error && videos.length > 0 && (
          <>
            <div className="video-grid-mobile mb-8">
              {videos.map((video, index) => (
                <VideoCard
                  key={`${video._id}-${index}`}
                  video={video}
                  showUploadDate={true}
                />
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && (
              <div className="flex justify-center">
                <button
                  onClick={loadMore}
                  disabled={loading}
                  className="btn-ph-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? 'Loading...' : 'Load More Videos'}
                </button>
              </div>
            )}
          </>
        )}

        {/* No Videos State */}
        {!loading && !error && videos.length === 0 && (
          <div className="flex flex-col items-center justify-center py-12">
            <FaClock className="text-ph-gray-600 text-6xl mb-4" />
            <h2 className="text-2xl font-bold text-ph-gray-400 mb-2">No Videos Found</h2>
            <p className="text-ph-gray-500 text-center max-w-md">
              {selectedCategory 
                ? 'No videos found in this category. Try selecting a different category or check back later.'
                : 'No videos have been uploaded yet. Check back later for new content.'
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
