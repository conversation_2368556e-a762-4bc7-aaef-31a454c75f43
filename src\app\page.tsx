'use client';

import VideoCard from './components/VideoCard';
import CategoryCard from './components/CategoryCard';
import RecommendedVideos from './components/RecommendedVideos';
import Link from 'next/link';
import { FaF<PERSON>, FaClock, FaChevronRight, FaThumbsUp } from 'react-icons/fa';
import { useState, useEffect } from 'react';
import axios from 'axios';

// Mock data for demonstration
const featuredVideos = [
  {
    id: '1',
    title: 'Hot blonde in action with her boyfriend',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 845, // 14:05
    views: 1250000,
    likes: 24500,
    slug: 'hot-blonde-in-action'
  },
  {
    id: '2',
    title: '<PERSON>runette gets wild in the bedroom',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1230, // 20:30
    views: 980000,
    likes: 18700,
    slug: 'brunette-gets-wild'
  },
  {
    id: '3',
    title: 'Steamy shower scene with two girls',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 720, // 12:00
    views: 1500000,
    likes: 32000,
    slug: 'steamy-shower-scene'
  },
  {
    id: '4',
    title: 'Office romance turns into something more',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 950, // 15:50
    views: 750000,
    likes: 15600,
    slug: 'office-romance'
  },
];

const trendingVideos = [
  {
    id: '5',
    title: 'Passionate couple on vacation',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1120, // 18:40
    views: 2100000,
    likes: 45000,
    slug: 'passionate-couple-vacation'
  },
  {
    id: '6',
    title: 'Fitness instructor gives special lesson',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 890, // 14:50
    views: 1800000,
    likes: 38000,
    slug: 'fitness-instructor-lesson'
  },
  {
    id: '7',
    title: 'Massage turns into something unexpected',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1350, // 22:30
    views: 2500000,
    likes: 52000,
    slug: 'massage-unexpected'
  },
  {
    id: '8',
    title: 'Pool party gets wild with multiple girls',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Adult+Content',
    duration: 1680, // 28:00
    views: 3200000,
    likes: 68000,
    slug: 'pool-party-wild'
  },
];

const popularCategories = [
  {
    name: 'Amateur',
    slug: 'amateur',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Amateur',
    videoCount: 12500
  },
  {
    name: 'MILF',
    slug: 'milf',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=MILF',
    videoCount: 8700
  },
  {
    name: 'Lesbian',
    slug: 'lesbian',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Lesbian',
    videoCount: 9300
  },
  {
    name: 'Threesome',
    slug: 'threesome',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Threesome',
    videoCount: 5600
  },
  {
    name: 'Blonde',
    slug: 'blonde',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Blonde',
    videoCount: 7800
  },
  {
    name: 'Anal',
    slug: 'anal',
    thumbnail: 'https://via.placeholder.com/640x360.png?text=Anal',
    videoCount: 6200
  },
];

export default function Home() {
  const [featuredVideosData, setFeaturedVideosData] = useState<any[]>([]);
  const [popularCategoriesData, setPopularCategoriesData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchHomeData = async () => {
      try {
        setLoading(true);

        // Fetch featured videos
        const featuredResponse = await axios.get('/api/videos?sort=featured&limit=4');
        if (featuredResponse.data && featuredResponse.data.videos) {
          setFeaturedVideosData(featuredResponse.data.videos);
        } else {
          // Fallback to mock data if API fails
          setFeaturedVideosData(featuredVideos);
        }

        // Fetch popular categories
        const categoriesResponse = await axios.get('/api/categories?withCounts=true&limit=6');
        if (categoriesResponse.data && categoriesResponse.data.categories) {
          setPopularCategoriesData(categoriesResponse.data.categories);
        } else {
          // Fallback to mock data if API fails
          setPopularCategoriesData(popularCategories);
        }
      } catch (error) {
        console.error('Error fetching home data:', error);
        // Fallback to mock data if API fails
        setFeaturedVideosData(featuredVideos);
        setPopularCategoriesData(popularCategories);
      } finally {
        setLoading(false);
      }
    };

    fetchHomeData();
  }, []);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800">
      {/* Hero Section */}
      <section className="mb-12">
        <div className="relative overflow-hidden">
          {/* Background with gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 via-blue-500/20 to-indigo-500/20"></div>
          <div className="relative bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-8 md:p-12 border border-gray-700 shadow-2xl">
            <div className="max-w-4xl">
              <h1 className="text-4xl md:text-6xl font-bold mb-6">
                <span className="bg-gradient-to-r from-purple-400 via-blue-500 to-indigo-500 bg-clip-text text-transparent">
                  Welcome to AdultHub
                </span>
              </h1>
              <p className="text-gray-300 text-xl mb-8 leading-relaxed">
                The ultimate destination for premium adult entertainment. Discover thousands of high-quality videos in stunning HD, updated daily with the hottest content.
              </p>
              <div className="flex flex-wrap gap-4">
                <Link
                  href="/categories"
                  className="flex items-center px-8 py-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-full hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/30 font-semibold"
                >
                  <FaFire className="mr-2" />
                  Browse Categories
                </Link>
                <Link
                  href="/search"
                  className="flex items-center px-8 py-4 bg-gradient-to-r from-gray-600 to-gray-700 text-white rounded-full hover:from-blue-500 hover:to-purple-600 transition-all duration-300 transform hover:scale-105 shadow-lg font-semibold"
                >
                  <FaClock className="mr-2" />
                  Search Videos
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Recommended Videos Section */}
      <section className="mb-16">
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
          <RecommendedVideos limit={4} title="Recommended For You" />
        </div>
      </section>

      {/* Featured Videos Section */}
      <section className="mb-16">
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-white flex items-center">
              <div className="bg-gradient-to-r from-purple-500 to-blue-500 p-2 rounded-lg mr-3 shadow-lg shadow-purple-500/30">
                <FaFire className="text-white text-xl" />
              </div>
              <span className="bg-gradient-to-r from-purple-400 to-blue-500 bg-clip-text text-transparent">
                Featured Videos
              </span>
            </h2>
            <Link
              href="/featured"
              className="flex items-center px-4 py-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white rounded-full hover:from-purple-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-purple-500/30"
            >
              View All <FaChevronRight className="ml-2" />
            </Link>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {!loading ? (
              featuredVideosData.map((video) => (
                <div key={video._id} className="group">
                  <VideoCard
                    id={video._id}
                    title={video.title}
                    thumbnail={video.thumbnailUrl}
                    duration={video.duration}
                    views={video.views}
                    likes={video.likes}
                    slug={video.slug}
                  />
                </div>
              ))
            ) : (
              // Show skeleton loaders while loading
              Array(4).fill(0).map((_, index) => (
                <div key={index} className="bg-gradient-to-r from-gray-700 to-gray-800 rounded-xl overflow-hidden animate-pulse border border-gray-600">
                  <div className="aspect-video bg-gradient-to-r from-gray-600 to-gray-700"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gradient-to-r from-gray-600 to-gray-700 rounded mb-3"></div>
                    <div className="h-3 bg-gradient-to-r from-gray-600 to-gray-700 rounded w-2/3"></div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Popular Categories Section */}
      <section className="mb-16">
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-white flex items-center">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 p-2 rounded-lg mr-3 shadow-lg shadow-blue-500/30">
                <FaClock className="text-white text-xl" />
              </div>
              <span className="bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
                Popular Categories
              </span>
            </h2>
            <Link
              href="/categories"
              className="flex items-center px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-full hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-blue-500/30"
            >
              View All <FaChevronRight className="ml-2" />
            </Link>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {!loading ? (
              popularCategoriesData.map((category) => (
                <div key={category._id || category.slug} className="group">
                  <CategoryCard
                    id={category._id || ''}
                    name={category.name}
                    slug={category.slug}
                    thumbnail={category.thumbnailUrl || `https://via.placeholder.com/640x360.png?text=${encodeURIComponent(category.name)}`}
                    videoCount={category.videoCount || 0}
                    description={category.description || ''}
                  />
                </div>
              ))
            ) : (
              // Show skeleton loaders while loading
              Array(6).fill(0).map((_, index) => (
                <div key={index} className="bg-gradient-to-r from-gray-700 to-gray-800 rounded-xl overflow-hidden animate-pulse border border-gray-600">
                  <div className="aspect-video bg-gradient-to-r from-gray-600 to-gray-700"></div>
                  <div className="p-4">
                    <div className="h-4 bg-gradient-to-r from-gray-600 to-gray-700 rounded mb-3"></div>
                    <div className="h-3 bg-gradient-to-r from-gray-600 to-gray-700 rounded w-1/3"></div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </section>

      {/* Most Liked Videos Section */}
      <section className="mb-16">
        <div className="bg-gradient-to-r from-gray-800 to-gray-900 rounded-2xl p-6 border border-gray-700 shadow-xl">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold text-white flex items-center">
              <div className="bg-gradient-to-r from-green-500 to-emerald-600 p-2 rounded-lg mr-3 shadow-lg shadow-green-500/30">
                <FaThumbsUp className="text-white text-xl" />
              </div>
              <span className="bg-gradient-to-r from-green-400 to-emerald-500 bg-clip-text text-transparent">
                Most Liked
              </span>
            </h2>
            <Link
              href="/search?sort=most-liked"
              className="flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-full hover:from-green-600 hover:to-emerald-700 transition-all duration-300 transform hover:scale-105 shadow-lg shadow-green-500/30"
            >
              View All <FaChevronRight className="ml-2" />
            </Link>
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {trendingVideos.map((video) => (
              <div key={video.id} className="group">
                <VideoCard {...video} />
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
}
