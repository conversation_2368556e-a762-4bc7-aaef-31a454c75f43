'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter } from 'next/navigation';
import VideoCard from '../components/VideoCard';
import { FaFilter, FaSearch, FaSpinner } from 'react-icons/fa';
import axios from 'axios';

interface Category {
  _id: string;
  name: string;
  slug: string;
}

interface Video {
  _id: string;
  title: string;
  thumbnailUrl: string;
  duration: number;
  views: number;
  likes: number;
  slug: string;
  categories: Category[];
}

export default function SearchPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const query = searchParams.get('q') || '';
  const categoryParam = searchParams.get('category') || 'all';
  const sortParam = searchParams.get('sort') || 'newest';

  const [searchQuery, setSearchQuery] = useState(query);
  const [selectedCategory, setSelectedCategory] = useState(categoryParam);
  const [sortBy, setSortBy] = useState(sortParam);
  const [videos, setVideos] = useState<Video[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [totalVideos, setTotalVideos] = useState(0);
  const [page, setPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  // Fetch categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await axios.get('/api/categories');
        if (response.data && response.data.categories) {
          setCategories(response.data.categories);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
      }
    };

    fetchCategories();
  }, []);

  // Fetch videos based on search parameters
  useEffect(() => {
    const fetchVideos = async () => {
      try {
        setLoading(true);

        // Build query parameters
        const params = new URLSearchParams();
        if (query) params.append('search', query);
        if (selectedCategory !== 'all') params.append('category', selectedCategory);
        params.append('sort', sortBy);
        params.append('page', page.toString());
        params.append('limit', '12');

        const response = await axios.get(`/api/videos?${params.toString()}`);

        if (response.data) {
          setVideos(response.data.videos || []);
          setTotalVideos(response.data.pagination?.total || 0);
          setTotalPages(response.data.pagination?.totalPages || 1);
        }
      } catch (error) {
        console.error('Error fetching videos:', error);
        setError('Failed to load videos. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchVideos();
  }, [query, selectedCategory, sortBy, page]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      // Update URL with search parameters
      const params = new URLSearchParams();
      params.append('q', searchQuery);
      if (selectedCategory !== 'all') params.append('category', selectedCategory);
      params.append('sort', sortBy);

      router.push(`/search?${params.toString()}`);
    }
  };

  const handleCategoryChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const category = e.target.value;
    setSelectedCategory(category);

    // Update URL with new category
    const params = new URLSearchParams(searchParams.toString());
    if (category === 'all') {
      params.delete('category');
    } else {
      params.set('category', category);
    }

    router.push(`/search?${params.toString()}`);
  };

  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const sort = e.target.value;
    setSortBy(sort);

    // Update URL with new sort parameter
    const params = new URLSearchParams(searchParams.toString());
    params.set('sort', sort);

    router.push(`/search?${params.toString()}`);
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);

    // Update URL with new page parameter
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', newPage.toString());

    router.push(`/search?${params.toString()}`);
  };

  return (
    <div>
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-white mb-6">
          Search Results for: <span className="text-red-500">{query}</span>
        </h1>

        {/* Search form */}
        <form onSubmit={handleSearch} className="mb-6">
          <div className="flex">
            <input
              type="text"
              placeholder="Search videos..."
              className="input-field flex-grow"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button type="submit" className="ml-2 p-3 bg-red-500 rounded hover:bg-red-600">
              <FaSearch />
            </button>
          </div>
        </form>

        {/* Filters */}
        <div className="flex flex-col md:flex-row justify-between gap-4 mb-6">
          <div className="flex items-center">
            <label className="text-white mr-2">Category:</label>
            <select
              className="bg-gray-800 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
              value={selectedCategory}
              onChange={handleCategoryChange}
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category._id} value={category._id}>
                  {category.name}
                </option>
              ))}
            </select>
          </div>

          <div className="flex items-center">
            <FaFilter className="text-red-500 mr-2" />
            <select
              className="bg-gray-800 text-white rounded px-3 py-2 focus:outline-none focus:ring-2 focus:ring-red-500"
              value={sortBy}
              onChange={handleSortChange}
            >
              <option value="newest">Newest</option>
              <option value="most-viewed">Most Viewed</option>
              <option value="most-liked">Most Liked</option>
              <option value="duration-asc">Shortest</option>
              <option value="duration-desc">Longest</option>
            </select>
          </div>
        </div>

        <div className="text-gray-400 mb-4">
          Found {totalVideos} videos
        </div>
      </div>

      {/* Loading state */}
      {loading && (
        <div className="flex justify-center items-center py-12">
          <FaSpinner className="animate-spin text-red-500 text-4xl" />
        </div>
      )}

      {/* Error state */}
      {error && !loading && (
        <div className="bg-red-900/50 text-white p-4 rounded-lg mb-6">
          <p>{error}</p>
        </div>
      )}

      {/* Videos Grid */}
      {!loading && !error && videos.length > 0 && (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {videos.map((video) => (
            <VideoCard
              key={video._id}
              id={video._id}
              title={video.title}
              thumbnail={video.thumbnailUrl}
              duration={video.duration}
              views={video.views}
              likes={video.likes}
              slug={video.slug}
            />
          ))}
        </div>
      )}

      {/* Show message if no videos */}
      {!loading && !error && videos.length === 0 && (
        <div className="bg-gray-800 rounded-lg p-8 text-center">
          <h3 className="text-xl font-semibold text-white mb-2">No videos found</h3>
          <p className="text-gray-400">Try different search terms or filters.</p>
        </div>
      )}

      {/* Pagination */}
      {!loading && !error && totalPages > 1 && (
        <div className="flex justify-center mt-8">
          <div className="flex gap-2">
            <button
              onClick={() => handlePageChange(Math.max(1, page - 1))}
              disabled={page === 1}
              className={`px-4 py-2 rounded-lg ${
                page === 1
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              Previous
            </button>

            {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
              // Show pages around the current page
              let pageNum;
              if (totalPages <= 5) {
                pageNum = i + 1;
              } else if (page <= 3) {
                pageNum = i + 1;
              } else if (page >= totalPages - 2) {
                pageNum = totalPages - 4 + i;
              } else {
                pageNum = page - 2 + i;
              }

              return (
                <button
                  key={pageNum}
                  onClick={() => handlePageChange(pageNum)}
                  className={`px-4 py-2 rounded-lg ${
                    page === pageNum
                      ? 'bg-red-600 text-white'
                      : 'bg-gray-800 text-white hover:bg-gray-700'
                  }`}
                >
                  {pageNum}
                </button>
              );
            })}

            <button
              onClick={() => handlePageChange(Math.min(totalPages, page + 1))}
              disabled={page === totalPages}
              className={`px-4 py-2 rounded-lg ${
                page === totalPages
                  ? 'bg-gray-800 text-gray-500 cursor-not-allowed'
                  : 'bg-gray-800 text-white hover:bg-gray-700'
              }`}
            >
              Next
            </button>
          </div>
        </div>
      )}
    </div>
  );
}
