'use client';

import { useState, useEffect } from 'react';
import { FaSave, FaTimes, FaUpload } from 'react-icons/fa';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import axios from 'axios';

// Interface for category
interface Category {
  _id: string;
  name: string;
  slug: string;
}

export default function AddVideo() {
  const router = useRouter();
  const [categories, setCategories] = useState<Category[]>([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    videoUrl: '',
    thumbnailUrl: '',
    duration: '',
    categories: [] as string[],
    tags: '',
    isPublished: true,
    isVerified: true
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // Fetch categories on component mount
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        console.log('Fetching categories...');
        const response = await axios.get('/api/categories');

        if (response.data && response.data.categories) {
          console.log('Categories fetched successfully:', response.data.categories.length);
          setCategories(response.data.categories);
        } else {
          console.error('No categories found in response:', response.data);
          // Set empty array to avoid undefined errors
          setCategories([]);
        }
      } catch (error) {
        console.error('Error fetching categories:', error);
        // Set empty array to avoid undefined errors
        setCategories([]);
      }
    };

    fetchCategories();
  }, []);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when field is edited
    if (errors[name]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[name];
        return newErrors;
      });
    }
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  const handleCategoryChange = (categoryId: string) => {
    setFormData(prev => {
      const categories = [...prev.categories];
      if (categories.includes(categoryId)) {
        return {
          ...prev,
          categories: categories.filter(c => c !== categoryId)
        };
      } else {
        return {
          ...prev,
          categories: [...categories, categoryId]
        };
      }
    });
  };

  // Handle file upload for video
  const handleVideoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Create a FormData object
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', 'adult_videos'); // Replace with your Cloudinary upload preset

    try {
      setUploading(true);

      // In a real implementation, you would upload to your server or a service like Cloudinary
      // This is a placeholder for demonstration
      // const response = await axios.post('https://api.cloudinary.com/v1_1/your-cloud-name/video/upload', formData);

      // For demo purposes, we'll simulate a successful upload after 2 seconds
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Simulate a response with a video URL
      const mockVideoUrl = `https://res.cloudinary.com/demo/video/upload/${file.name}`;

      setFormData(prev => ({
        ...prev,
        videoUrl: mockVideoUrl
      }));

      setUploading(false);
    } catch (error) {
      console.error('Error uploading video:', error);
      setUploading(false);
      alert('Failed to upload video. Please try again.');
    }
  };

  // Handle file upload for thumbnail
  const handleThumbnailUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Create a FormData object
    const formData = new FormData();
    formData.append('file', file);
    formData.append('upload_preset', 'adult_thumbnails'); // Replace with your Cloudinary upload preset

    try {
      setUploading(true);

      // In a real implementation, you would upload to your server or a service like Cloudinary
      // This is a placeholder for demonstration
      // const response = await axios.post('https://api.cloudinary.com/v1_1/your-cloud-name/image/upload', formData);

      // For demo purposes, we'll simulate a successful upload after 1 second
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Simulate a response with a thumbnail URL
      const mockThumbnailUrl = `https://res.cloudinary.com/demo/image/upload/${file.name}`;

      setFormData(prev => ({
        ...prev,
        thumbnailUrl: mockThumbnailUrl
      }));

      setUploading(false);
    } catch (error) {
      console.error('Error uploading thumbnail:', error);
      setUploading(false);
      alert('Failed to upload thumbnail. Please try again.');
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.title.trim()) {
      newErrors.title = 'Title is required';
    }

    if (!formData.videoUrl.trim()) {
      newErrors.videoUrl = 'Video URL is required';
    }

    if (!formData.thumbnailUrl.trim()) {
      newErrors.thumbnailUrl = 'Thumbnail URL is required';
    }

    if (!formData.duration.trim()) {
      newErrors.duration = 'Duration is required';
    } else if (isNaN(Number(formData.duration))) {
      newErrors.duration = 'Duration must be a number (in seconds)';
    }

    if (formData.categories.length === 0) {
      newErrors.categories = 'At least one category is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (validateForm()) {
      try {
        setLoading(true);

        // Prepare tags array from comma-separated string
        const tags = formData.tags
          .split(',')
          .map(tag => tag.trim())
          .filter(tag => tag.length > 0);

        // Create a slug from the title
        const slug = formData.title
          .toLowerCase()
          .replace(/[^\w\s]/gi, '')
          .replace(/\s+/g, '-')
          .concat('-', Date.now().toString().slice(-4));

        // Prepare the data to send to the API
        const videoData = {
          title: formData.title,
          description: formData.description,
          slug,
          duration: parseInt(formData.duration),
          thumbnailUrl: formData.thumbnailUrl,
          videoUrl: formData.videoUrl,
          categories: formData.categories,
          tags,
          isPublished: formData.isPublished,
          isVerified: formData.isVerified,
          uploadedBy: 'admin', // In a real app, this would be the user's ID
          views: 0,
          likes: 0,
          dislikes: 0
        };

        // Send the data to the API
        const response = await axios.post('/api/videos', videoData);

        // Redirect to the videos list page
        router.push('/admin/videos');

      } catch (error) {
        console.error('Error adding video:', error);
        alert('Failed to add video. Please try again.');
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-white">Add New Video</h1>
        <Link href="/admin/videos" className="btn-secondary flex items-center">
          <FaTimes className="mr-2" /> Cancel
        </Link>
      </div>

      <form onSubmit={handleSubmit} className="bg-gray-800 rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Left Column */}
          <div>
            <div className="mb-4">
              <label className="block text-white mb-2">Title *</label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleChange}
                className={`input-field w-full ${errors.title ? 'border border-red-500' : ''}`}
                placeholder="Enter video title"
              />
              {errors.title && <p className="text-red-500 text-sm mt-1">{errors.title}</p>}
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Description</label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="input-field w-full h-32"
                placeholder="Enter video description"
              ></textarea>
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Video File *</label>
              <div className="flex items-center">
                <input
                  type="file"
                  id="videoFile"
                  accept="video/*"
                  onChange={handleVideoUpload}
                  className="hidden"
                  disabled={uploading}
                />
                <label
                  htmlFor="videoFile"
                  className={`btn-secondary flex items-center cursor-pointer ${uploading ? 'opacity-50' : ''}`}
                >
                  <FaUpload className="mr-2" /> {uploading ? 'Uploading...' : 'Upload Video'}
                </label>
                {formData.videoUrl && (
                  <span className="ml-3 text-green-500">Video uploaded successfully</span>
                )}
              </div>
              {errors.videoUrl && <p className="text-red-500 text-sm mt-1">{errors.videoUrl}</p>}
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Thumbnail Image *</label>
              <div className="flex items-center">
                <input
                  type="file"
                  id="thumbnailFile"
                  accept="image/*"
                  onChange={handleThumbnailUpload}
                  className="hidden"
                  disabled={uploading}
                />
                <label
                  htmlFor="thumbnailFile"
                  className={`btn-secondary flex items-center cursor-pointer ${uploading ? 'opacity-50' : ''}`}
                >
                  <FaUpload className="mr-2" /> {uploading ? 'Uploading...' : 'Upload Thumbnail'}
                </label>
                {formData.thumbnailUrl && (
                  <span className="ml-3 text-green-500">Thumbnail uploaded successfully</span>
                )}
              </div>
              {errors.thumbnailUrl && <p className="text-red-500 text-sm mt-1">{errors.thumbnailUrl}</p>}
              {formData.thumbnailUrl && (
                <div className="mt-2">
                  <img src={formData.thumbnailUrl} alt="Thumbnail preview" className="w-32 h-auto rounded" />
                </div>
              )}
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Duration (in seconds) *</label>
              <input
                type="text"
                name="duration"
                value={formData.duration}
                onChange={handleChange}
                className={`input-field w-full ${errors.duration ? 'border border-red-500' : ''}`}
                placeholder="e.g., 600 for 10 minutes"
              />
              {errors.duration && <p className="text-red-500 text-sm mt-1">{errors.duration}</p>}
            </div>
          </div>

          {/* Right Column */}
          <div>
            <div className="mb-4">
              <label className="block text-white mb-2">Categories *</label>
              <div className="bg-gray-700 p-4 rounded max-h-64 overflow-y-auto">
                {categories === undefined ? (
                  <div className="flex flex-col items-center justify-center py-4">
                    <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-red-500 mb-2"></div>
                    <p className="text-gray-400">Loading categories...</p>
                  </div>
                ) : categories.length > 0 ? (
                  <div className="grid grid-cols-2 gap-2">
                    {categories.map((category) => (
                      <div key={category._id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`category-${category._id}`}
                          checked={formData.categories.includes(category._id)}
                          onChange={() => handleCategoryChange(category._id)}
                          className="mr-2"
                        />
                        <label htmlFor={`category-${category._id}`} className="text-gray-300 cursor-pointer">
                          {category.name}
                        </label>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-4">
                    <p className="text-yellow-400 mb-2">No categories found</p>
                    <p className="text-gray-400 text-sm">
                      Please make sure MongoDB is running and categories are created.
                      <br />
                      You can run the create-categories.js script to add initial categories.
                    </p>
                  </div>
                )}
              </div>
              {errors.categories && <p className="text-red-500 text-sm mt-1">{errors.categories}</p>}
            </div>

            <div className="mb-4">
              <label className="block text-white mb-2">Tags (comma separated)</label>
              <input
                type="text"
                name="tags"
                value={formData.tags}
                onChange={handleChange}
                className="input-field w-full"
                placeholder="e.g., blonde, amateur, couple"
              />
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isPublished"
                  name="isPublished"
                  checked={formData.isPublished}
                  onChange={handleCheckboxChange}
                  className="mr-2"
                />
                <label htmlFor="isPublished" className="text-white cursor-pointer">
                  Published (video will be visible on the site)
                </label>
              </div>
            </div>

            <div className="mb-4">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="isVerified"
                  name="isVerified"
                  checked={formData.isVerified}
                  onChange={handleCheckboxChange}
                  className="mr-2"
                />
                <label htmlFor="isVerified" className="text-white cursor-pointer">
                  Age Verified (confirm all performers are 18+)
                </label>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-6 flex justify-end">
          <button
            type="submit"
            className={`btn-primary flex items-center ${loading ? 'opacity-50 cursor-not-allowed' : ''}`}
            disabled={loading || uploading}
          >
            <FaSave className="mr-2" /> {loading ? 'Saving...' : 'Save Video'}
          </button>
        </div>
      </form>
    </div>
  );
}
